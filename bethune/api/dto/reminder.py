import json
from enum import StrEnum

from pydantic import BaseModel, Field, computed_field

from bethune.api.dto.base import AuditMixin
from bethune.api.dto.base import BusinessType
from bethune.api.dto.base import ExtendedAuditMixin
from bethune.api.dto.base import InsuranceType
from bethune.model.reminder import ReminderConfig as ReminderConfigModel
from bethune.model.reminder import ReminderMessage as ReminderMessageModel
from bethune.util import get_text
from bethune.util.date import get_current_datetime


class ReminderType(StrEnum):
    """提醒类型"""

    BROKER = "BROKER"
    CUSTOMER = "CUSTOMER"


class NotifyMethod(StrEnum):
    """通知方式"""

    INBOX = "INBOX"
    EMAIL = "EMAIL"
    SMS = "SMS"


class ReminderConfigBase(BaseModel):
    broker_id: int | None = None
    business_type: BusinessType
    reminder_type: ReminderType
    first_reminder_days: int
    second_reminder_days: int | None = None
    notify_methods: list[str]  # type: ignore
    enabled: bool | None = None


class ReminderConfigCreate(ReminderConfigBase):

    def to_model(self, broker_id: int):
        model = ReminderConfigModel(**self.model_dump(exclude_unset=True))
        model.broker_id = broker_id  # type: ignore
        model.notify_methods_list = self.notify_methods  # type: ignore
        return model


class ReminderConfigCreateOrUpdate(BaseModel):
    id: int | None = None
    first_reminder_days: int | None = None
    second_reminder_days: int | None = None
    business_type: BusinessType | None = None
    reminder_type: ReminderType | None = None
    notify_methods: list[NotifyMethod] | None = None
    enabled: bool | None = None
    broker_id: int | None = None

    def to_update_model(self, broker_id: int):
        model = ReminderConfigModel(**self.model_dump(exclude_unset=True, exclude_none=True, exclude={"broker"}))
        model.broker_id = broker_id
        if self.notify_methods is not None:
            model.notify_methods_list = self.notify_methods  # type: ignore
        if not self.second_reminder_days:
            model.second_reminder_days = 0
        model.updated_at = get_current_datetime()
        return model

    def to_create_model(self, broker_id: int):
        model = ReminderConfigModel(**self.model_dump(exclude_unset=True))
        model.broker_id = broker_id
        model.notify_methods_list = self.notify_methods  # type: ignore
        return model


class ReminderConfig(ReminderConfigBase, AuditMixin):
    id: int

    @classmethod
    def from_model(cls, model: ReminderConfigModel):
        model_dict = model.model_dump()
        model_dict.update(
            notify_methods=model.notify_methods_list,
        )
        return cls(**model_dict)

    @classmethod
    def from_models(cls, models: list[ReminderConfigModel]):
        return [cls.from_model(model) for model in models]


class ReminderConfigList(ReminderConfigBase, AuditMixin):
    id: int

    @classmethod
    def from_model(cls, model: ReminderConfigModel):
        model_dict = model.model_dump()
        model_dict.update(
            notify_methods=model.notify_methods_list,
        )
        return cls(**model_dict)

    @classmethod
    def from_models(cls, models: list[ReminderConfigModel]):
        return [cls.from_model(model) for model in models]


class ReminderMessageBase(BaseModel):
    business_type: BusinessType | None = None
    insurance_type: InsuranceType | None = None
    content: str | None = None
    extra_content: str | None = None
    receiver_id: int | None = None
    sender_id: int | None = None
    context: str | None = None  # 存储JSON格式的上下文信息


class ReminderMessageCreate(ReminderMessageBase):
    def to_model(self):
        return ReminderMessageModel(**self.model_dump(exclude_unset=True))


class ReminderMessage(ReminderMessageBase, ExtendedAuditMixin):
    id: int
    read_status: int = 0

    @classmethod
    def from_model(cls, model: ReminderMessageModel):
        return cls(**model.model_dump(exclude={"delete_at"}))


class ReminderMessageList(ExtendedAuditMixin):
    id: int
    business_type: BusinessType | None = None
    insurance_type: InsuranceType | None = None
    extra_content: str | None = None
    receiver_id: int | None = None
    sender_id: int | None = None
    read_status: int = 0
    extra_content_dict: dict = {}

    original_content: str | None = Field(None, exclude=True)
    context: str | None = Field(None, exclude=True)

    @computed_field
    @property
    def content(self) -> str:
        if self.context:
            try:
                context_data = json.loads(self.context)
                msgstr_template = get_text(self.original_content)
                return msgstr_template.format(**context_data)
            except (json.JSONDecodeError, KeyError, ValueError):
                return get_text(self.original_content)
        else:
            return get_text(self.original_content) if self.original_content else ""

    @classmethod
    def from_model(cls, model: ReminderMessageModel):
        model_data = model.model_dump(exclude={"delete_at", "content"})
        new_instance = cls(**model_data)
        new_instance.original_content = model.content
        new_instance.context = model.context
        if new_instance.extra_content:
            new_instance.extra_content_dict = json.loads(new_instance.extra_content)
        return new_instance

    @classmethod
    def from_models(cls, models: list[ReminderConfigModel]):
        return [cls.from_model(model) for model in models]


class ReminderMessageQuery(BaseModel):
    read_status: int | None = None
    receiver_id: int | None = None
    sender_id: int | None = None

    def to_model(self):
        model = ReminderMessageModel(**self.model_dump(exclude_unset=True, exclude_none=True))
        model.is_deleted = False
        return model
