from bethune.api.dependencies.authorization import BaseServiceContext
from bethune.model import Broker
from bethune.model import BrokerageUser
from bethune.service.insurance import BrokerService
from bethune.service.insurance import CustomerService
from bethune.service.insurance import InsuranceApplicationService
from bethune.service.insurance import InsuranceCompanyService
from bethune.service.insurance import InsuranceConsultationService
from bethune.service.insurance import InsurancePolicyService
from bethune.service.insurance import PromotionMaterialService
from bethune.service.insurance import ServiceFactory
from bethune.service.insurance import UserFeedbackService
from bethune.service.insurance.brokerage import BrokerageService
from bethune.service.insurance.insurance_consultation_application import InsuranceConsultationApplicationService
from bethune.service.insurance.insurance_consultation_customer import InsuranceConsultationCustomerService
from bethune.service.insurance.lead import LeadService
from bethune.service.reminder import ReminderConfigService
from bethune.service.reminder import ReminderMessageService
from bethune.service.reminder import ServiceFactory as ReminderServiceFactory


class ServiceContext(BaseServiceContext):
    def __init__(self):
        super().__init__()
        self._customer_service = None
        self._insurance_application_service = None
        self._insurance_policy_service = None
        self._insurance_company_service = None
        self._broker_service = None
        self._current_broker = None
        self._lead_service = None
        self._reminder_message_service = None
        self._broker_reminder_settings_service = None
        self._message_template_service = None
        self._reminder_config_service = None
        self._user_feedback_service = None
        self._brokerage_service = None
        self._current_brokerage_user = None
        self._promotion_material_service = None
        self._insurance_consultation_service = None
        self._insurance_consultation_customer_service = None
        self._insurance_consultation_application_service = None

    @property
    def current_broker(self) -> Broker:
        if not self._current_broker:
            self._current_broker = self.broker_service.get_by_user_id(self.current_user.id)  # type: ignore
        return self._current_broker

    @property
    def current_brokerage_user(self) -> BrokerageUser:
        if not self._current_brokerage_user:
            self._current_brokerage_user = self.brokerage_service.get_by_user_id(self.current_user.id)  # type: ignore
        return self._current_brokerage_user

    @property
    def customer_service(self) -> CustomerService:
        if not self._customer_service:
            self._customer_service = ServiceFactory.create_customer_service()
        return self._customer_service

    @property
    def insurance_application_service(self) -> InsuranceApplicationService:
        if not self._insurance_application_service:
            self._insurance_application_service = ServiceFactory.create_insurance_application_service()
        return self._insurance_application_service

    @property
    def insurance_policy_service(self) -> InsurancePolicyService:
        if not self._insurance_policy_service:
            self._insurance_policy_service = ServiceFactory.create_insurance_policy_service()
        return self._insurance_policy_service

    @property
    def insurance_company_service(self) -> InsuranceCompanyService:
        if not self._insurance_company_service:
            self._insurance_company_service = ServiceFactory.create_insurance_company_service()
        return self._insurance_company_service

    @property
    def broker_service(self) -> BrokerService:
        if not self._broker_service:
            self._broker_service = ServiceFactory.create_broker_service()
        return self._broker_service

    @property
    def lead_service(self) -> LeadService:
        if not self._lead_service:
            self._lead_service = ServiceFactory.create_lead_service()
        return self._lead_service

    @property
    def reminder_config_service(self) -> "ReminderConfigService":
        if not self._reminder_config_service:
            self._reminder_config_service = ReminderServiceFactory.create_reminder_config_service()
        return self._reminder_config_service

    @property
    def reminder_message_service(self) -> "ReminderMessageService":
        if not self._reminder_message_service:
            self._reminder_message_service = ReminderServiceFactory.create_reminder_message_service()
        return self._reminder_message_service

    @property
    def user_feedback_service(self) -> "UserFeedbackService":
        if not self._user_feedback_service:
            self._user_feedback_service = ServiceFactory.create_user_feedback_service()
        return self._user_feedback_service

    @property
    def brokerage_service(self) -> BrokerageService:
        if not self._brokerage_service:
            self._brokerage_service = ServiceFactory.create_brokerage_service()
        return self._brokerage_service

    @property
    def insurance_consultation_service(self) -> InsuranceConsultationService:
        if not self._insurance_consultation_service:
            self._insurance_consultation_service = ServiceFactory.create_insurance_consultation_service()
        return self._insurance_consultation_service

    @property
    def promotion_material_service(self) -> PromotionMaterialService:
        if not self._promotion_material_service:
            self._promotion_material_service = ServiceFactory.create_promotion_service()
        return self._promotion_material_service

    @property
    def insurance_consultation_customer_service(self) -> InsuranceConsultationCustomerService:
        if not self._insurance_consultation_customer_service:
            self._insurance_consultation_customer_service = (
                ServiceFactory.create_insurance_consultation_customer_service()
            )
        return self._insurance_consultation_customer_service

    @property
    def insurance_consultation_application_service(self) -> InsuranceConsultationApplicationService:
        if not self._insurance_consultation_application_service:
            self._insurance_consultation_application_service = (
                ServiceFactory.create_insurance_consultation_application_service()
            )
        return self._insurance_consultation_application_service
