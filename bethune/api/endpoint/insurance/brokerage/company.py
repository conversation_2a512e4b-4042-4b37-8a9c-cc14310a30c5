from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends
from fastapi import Security
from sqlalchemy.exc import IntegrityError

from bethune.api.dto.auth import VerifyVerificationEmailCode
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.brokerage import BrokerageCreatingRequest
from bethune.api.dto.brokerage import BrokerageResponse
from bethune.api.dto.brokerage import BrokerageTrialApplicationCreatingRequest
from bethune.api.dto.brokerage import BrokerageTrialApplicationResponse
from bethune.api.dto.brokerage import BrokerageUpdatingRequest
from bethune.api.dto.brokerage_user import BrokerageUserResponse
from bethune.api.dto.trial_company_user import TrialApplicationToCompanyRequest
from bethune.api.dto.user import UserCreate
from bethune.api.endpoint.core.service_context import ServiceContext as CoreServiceContext
from bethune.api.endpoint.insurance.service_context import ServiceContext as InsuranceServiceContext
from bethune.api.endpoint.system.service_context import ServiceContext as SystemServiceContext
from bethune.error import DataValidationError
from bethune.model import Brokerage
from bethune.model.brokerage import BrokerageTrialApplication
from bethune.model.brokerage_user import BrokerageUser as BrokerageUserModel
from bethune.model.system import ROLE_BROKERAGE_ADMIN_ID
from bethune.model.system import UserStatus
from bethune.model.system import UserType

api_router = APIRouter(prefix="/company", tags=["brokerage", "company"])


# TODO 迁移到endpoint/core以便重用，但是可能前端不需要这个API，不需要就删掉
@api_router.post("/trial_application/verify_email_code")
async def _(
    sc_core: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    verify_email: VerifyVerificationEmailCode,
):
    await sc_system.user_service.check_user_exists(str(verify_email.email))
    await sc_core.verification_code_service.verify_verification_code(verify_email.email, verify_email.verification_code)
    return BaseResponse.ok(verify_email.email)


@api_router.post(
    "/trial_application",
    summary="apply a new trial",
    response_model=BaseResponse[BrokerageTrialApplicationResponse],
)
async def _(
    trial_application: BrokerageTrialApplicationCreatingRequest,
    sc_core: Annotated[CoreServiceContext, Depends()],
    sc_system: Annotated[SystemServiceContext, Depends()],
    sc: Annotated[InsuranceServiceContext, Depends()],
) -> BaseResponse[BrokerageTrialApplicationResponse]:
    await sc_system.user_service.check_user_exists(str(trial_application.contact_email))
    await verify_verification_code(sc_core, trial_application)
    try:
        return BaseResponse.ok(
            BrokerageTrialApplicationResponse.from_model(sc.brokerage_service.create(trial_application.to_model()))
        )
    except IntegrityError:
        raise DataValidationError("both name and contact email are unique")


@api_router.post(
    "/trial_application_skip_verification",
    summary="apply a new trial skip verification",
    response_model=BaseResponse[BrokerageTrialApplicationResponse],
)
async def _(
    trial_application: BrokerageTrialApplicationCreatingRequest,
    sc_system: Annotated[SystemServiceContext, Depends()],
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:company:create"]),
) -> BaseResponse[BrokerageTrialApplicationResponse]:
    await sc_system.user_service.check_user_exists(str(trial_application.contact_email))
    try:
        return BaseResponse.ok(
            BrokerageTrialApplicationResponse.from_model(sc.brokerage_service.create(trial_application.to_model()))
        )
    except IntegrityError:
        raise DataValidationError("both name and contact email are unique")


async def verify_verification_code(sc_core, trial_application):
    await sc_core.verification_code_service.verify_verification_code(
        trial_application.contact_email, trial_application.verification_code
    )


@api_router.get(
    "/get_trial_application_list",
    summary="query trial list",
    response_model=BaseResponse[list[BrokerageTrialApplicationResponse]],
)
async def _(
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:company:create"]),
) -> BaseResponse[list[BrokerageTrialApplicationResponse]]:
    return BaseResponse.ok(
        [
            BrokerageTrialApplicationResponse.from_model(trial_application)
            for trial_application in sc.brokerage_service.get_by_example(BrokerageTrialApplication)
        ]
    )


@api_router.post(
    "",
    summary="create a new brokerage company",
    response_model=BaseResponse[BrokerageResponse],
)
async def _(
    brokerage: BrokerageCreatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:company:create"]),
) -> BaseResponse[BrokerageResponse]:
    try:
        return BaseResponse.ok(BrokerageResponse.from_model(sc.brokerage_service.create(brokerage.to_model())))
    except IntegrityError:
        raise DataValidationError("both name and contact email are unique")


@api_router.get(
    "/{id}",
    summary="get brokerage by id",
    response_model=BaseResponse[BrokerageResponse],
)
async def _(
    id: int,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create),
) -> BaseResponse[BrokerageResponse]:
    return BaseResponse.ok(BrokerageResponse.from_model(sc.brokerage_service.get_by_id(Brokerage, id)))


@api_router.put(
    "/{id}",
    summary="update brokerage company information",
    response_model=BaseResponse[BrokerageResponse],
)
async def _(
    id: int,
    brokerage: BrokerageUpdatingRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:company:edit"]),
) -> BaseResponse[BrokerageResponse]:
    try:
        return BaseResponse.ok(BrokerageResponse.from_model(sc.brokerage_service.update(brokerage.to_model(id))))
    except IntegrityError:
        raise DataValidationError("both name and contact email are unique")


@api_router.post(
    "/create_company_and_admin",
    summary="根据试用申请ID创建正式经纪公司和管理员用户",
    response_model=BaseResponse[dict],
)
async def create_company_and_admin(
    request: TrialApplicationToCompanyRequest,
    sc: InsuranceServiceContext = Security(InsuranceServiceContext.create, scopes=["brokerage:company:create"]),
    sc_system: SystemServiceContext = Security(SystemServiceContext.create, scopes=["brokerage:personnel:create"]),
) -> BaseResponse[dict]:
    trial_application = sc.brokerage_service.get_by_id(BrokerageTrialApplication, request.trial_application_id)
    try:
        await sc_system.user_service.check_user_exists(trial_application.contact_email)
    except DataValidationError:
        raise DataValidationError(f"邮箱 {trial_application.contact_email} 已被注册，请使用其他邮箱")

    brokerage_request = BrokerageCreatingRequest(
        name=trial_application.name,
        contact_name=trial_application.contact_name,
        contact_email=trial_application.contact_email,
        contact_phone=trial_application.contact_phone,
    )

    try:
        created_brokerage = sc.brokerage_service.create(brokerage_request.to_model())
    except IntegrityError:
        raise DataValidationError("经纪公司名称或联系邮箱已存在")

    user_create = UserCreate(
        name=trial_application.contact_name,
        email=trial_application.contact_email,
        password="123456",
        user_type=UserType.SAAS,
        status=UserStatus.ACTIVE,
        mobile=trial_application.contact_phone,
    )

    created_user = sc_system.user_service.create(user_create.to_model())
    sc_system.user_service.set_roles(created_user.id, [ROLE_BROKERAGE_ADMIN_ID])  # type: ignore
    brokerage_user = BrokerageUserModel(
        user_id=created_user.id,
        brokerage_id=created_brokerage.id,
        name=trial_application.contact_name,
        phone=trial_application.contact_phone,
    )
    created_brokerage_user = sc.brokerage_service.create(brokerage_user)

    # 重新获取BrokerageUser以确保关联数据被正确加载
    brokerage_user_with_relations = sc.brokerage_service.get_by_id(
        BrokerageUserModel, created_brokerage_user.id  # type:  ignore
    )
    brokerage_user_response = BrokerageUserResponse.from_model(brokerage_user_with_relations)

    return BaseResponse.ok({"brokerage_user": brokerage_user_response.model_dump()})
