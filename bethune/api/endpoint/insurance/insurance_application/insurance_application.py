import uuid
from datetime import datetime
from datetime import timedelta
from datetime import UTC
from typing import Annotated
from typing import Any
from urllib.parse import quote as url_quote

import httpx
from fastapi import APIRouter
from fastapi import BackgroundTasks
from fastapi import Depends
from fastapi import File
from fastapi import Path
from fastapi import Query
from fastapi import Security
from fastapi import UploadFile
from fastapi.responses import FileResponse
from fastapi.responses import StreamingResponse

from ..edoc import EdocFileHelper
from ..service_context import ServiceContext
from .state_machine import application_state_transition
from bethune.api.dependencies import get_json_extractor
from bethune.api.dependencies.authorization import check_has_cached_application_id
from bethune.api.dependencies.authorization import check_not_shared_link
from bethune.api.dependencies.authorization import check_shared_link_permission
from bethune.api.dependencies.authorization import extract_serial_number
from bethune.api.dependencies.authorization import share_link_cache_key
from bethune.api.dependencies.json_extractor import JsonExtractor
from bethune.api.dto.auth import TokenData
from bethune.api.dto.base import BaseResponse
from bethune.api.dto.base import Pagination
from bethune.api.dto.broker import InsuranceConsultationApplication
from bethune.api.dto.insurance_application import EdocType
from bethune.api.dto.insurance_application import ExportPdfRequest
from bethune.api.dto.insurance_application import InsuranceApplication
from bethune.api.dto.insurance_application import InsuranceApplicationCreate
from bethune.api.dto.insurance_application import InsuranceApplicationList
from bethune.api.dto.insurance_application import InsuranceApplicationQuery
from bethune.api.dto.insurance_application import InsuranceApplicationUpdate
from bethune.api.dto.insurance_application import InsuranceStatusEnum
from bethune.api.dto.insurance_application import QuoteRequest
from bethune.api.dto.insurance_application import RejectionRequest
from bethune.api.dto.insurance_application import ShareLinkRequest
from bethune.api.dto.insurance_application import to_broker_view_statuses
from bethune.api.dto.insurance_application import to_brokerage_view_statuses
from bethune.api.dto.insurance_application import UnderwrittenRequest
from bethune.api.endpoint.core.service_context import (
    ServiceContext as CoreServiceContext,
)
from bethune.api.endpoint.insurance.insurance_application.helper import enrich_broker_info
from bethune.api.endpoint.insurance.insurance_application.helper import enrich_lead_info
from bethune.api.endpoint.insurance.insurance_application.helper import to_insurance_application_dto
from bethune.api.error import UnauthorizedError
from bethune.api.mappings import INSURANCE_MAPPINGS
from bethune.db.redis import get_redis
from bethune.error.errors import NotFoundError
from bethune.model import Customer as CustomerModel
from bethune.model.base import GenderEnum
from bethune.model.insurance import InsuranceApplication as InsuranceApplicationModel
from bethune.model.insurance import InsuranceApplicationQueryFilters
from bethune.model.insurance import InsuranceApplicationStatus
from bethune.model.lead import Lead
from bethune.service.insurance.application_state_machine import TriggerEnum
from bethune.settings import settings
from bethune.util import wiith
from bethune.util.date import get_current_datetime
from bethune.util.path import concat_path


api_router = APIRouter(prefix="/insurance-application", tags=["insurance-application"])


def load_and_check_application(
    id: Annotated[int, Path(description="insurance application id")],
    sc: ServiceContext = Security(ServiceContext.create),
) -> InsuranceApplicationModel:
    application = sc.insurance_application_service.get_by_id(id)
    if sc.current_user.is_broker_role:
        if application.broker_id == sc.current_broker.id:
            return application
    if sc.current_user.is_broker_support_role or sc.current_user.is_brokerage_admin_role:
        if application.brokerage_id == sc.current_brokerage_user.brokerage_id:
            return application
    raise UnauthorizedError("You are not allowed to update this application")


def get_serial_number(
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> str:
    if application.serial_number is None:
        raise NotFoundError("Can't find serial number for this application")
    return application.serial_number


@api_router.post(
    "",
    summary="create insurance application",
    response_model=BaseResponse[InsuranceApplication],
    tags=["broker"],
)
async def _(
    extractor: JsonExtractor = Depends(get_json_extractor),
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:create"]),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create),
) -> BaseResponse[InsuranceApplication]:
    broker = sc.current_broker
    application_create = extractor.extract(InsuranceApplicationCreate, INSURANCE_MAPPINGS)

    if application_create.customer_id is None:
        customer = sc.customer_service.check_existing_customer(
            CustomerModel(
                name=application_create.customer_name,
                gender=(GenderEnum(application_create.customer_gender) if application_create.customer_gender else None),
                email=(application_create.email.strip().lower() if application_create.email else None),
                province=application_create.province,
                city=application_create.city,
                postal_code=application_create.postal_code,
                address=(application_create.address if application_create.address else ""),
                phone=application_create.phone,
                birthday=application_create.birthday,
                move_in_date=application_create.move_in_date,
                broker_id=broker.id,
            )
        )
        application_create.customer_id = customer.id  # type: ignore

    application_create.broker_id = broker.id
    application_create.brokerage_id = broker.brokerage_id
    if application_create.brokerage_id is None:
        application_create.status = InsuranceStatusEnum.PENDING_UNDERWRITTEN
    else:
        application_create.status = InsuranceStatusEnum.PENDING_QUOTE

    application_model = sc.insurance_application_service.create(
        application_create.to_model(core_sc.reference_code_service)
    )

    if application_create.insurance_consultation_id:
        sc.insurance_consultation_application_service.create(
            InsuranceConsultationApplication(
                insurance_consultation=application_create.insurance_consultation_id,
                application_id=application_model.id,
            ).to_model()
        )
        sc.insurance_consultation_service.mark_as_completed(
            application_create.broker_id, application_create.insurance_consultation_id  # type: ignore
        )

    insurance_application = to_insurance_application_dto(sc, application_model)
    sc.insurance_application_service.save_json(
        {
            **{"insurance_application": insurance_application.model_dump(mode="json")},
            **extractor.json_data,
        },
        insurance_application.serial_number,  # type: ignore
    )

    return BaseResponse.ok(insurance_application)


@api_router.put(
    "/{id:int}",
    summary="update insurance application by id",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_shared_link_permission)],
    tags=["broker"],
)
async def _(
    serial_number_in_token: Annotated[str | None, Depends(extract_serial_number)],
    background_tasks: BackgroundTasks,
    extractor: JsonExtractor = Depends(get_json_extractor),
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    sc_core: CoreServiceContext = Security(CoreServiceContext.create),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    application_update = extractor.extract(InsuranceApplicationUpdate, INSURANCE_MAPPINGS)
    application.sqlmodel_update(application_update.model_dump(exclude_unset=True))
    application_state_transition(application, sc, TriggerEnum.EDIT, extractor.json_data)
    # notify broker if customer has submitted their insurance application
    if serial_number_in_token is not None and extractor.json_data.get("completed", False):
        title = f"Client {application.customer_name} Has Submitted Their Insurance Application — Action Required"
        template = "customer_insurance_application_submission.html"
        _send_notificaiton_email_to_broker(
            background_tasks,
            sc,
            sc_core,
            application,
            title,
            template,
        )
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


@api_router.delete(
    "/{id:int}",
    summary="delete insurance application by id",
    response_model=BaseResponse[InsuranceApplication],
)
async def _(
    id: int,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:delete"]),
) -> BaseResponse[InsuranceApplication]:
    return BaseResponse.ok(to_insurance_application_dto(sc, sc.insurance_application_service.mark_as_deleted(id)))


@api_router.get(
    "/recent-quota-count",
    summary="get recent insurance application, customer 30 days ago",
    response_model=BaseResponse[dict],
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker"],
)
async def _(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
) -> BaseResponse[dict]:
    broker_id: int = sc.current_broker.id  # type: ignore
    return BaseResponse.ok(
        {
            "customer_count": sc.customer_service.count_new_customers(broker_id),
            "insurance_application_count": sc.insurance_application_service.count_recent_applications(broker_id),
        }
    )


@api_router.post(
    "/{id:int}/request-quote",
    summary="request quote for insurance application",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker"],
)
async def request_quote(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    application.request_quote_at = get_current_datetime()
    application_state_transition(application, sc, TriggerEnum.REQUEST_QUOTE)
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


@api_router.post(
    "/{id:int}/withdraw",
    summary="withdraw insurance application",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker"],
)
async def withdraw(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    application.request_quote_at = None
    application.operator_id = None
    application.operator_name = None
    # shoule clear following fields?
    application.quote_at = None
    application.underwrite_at = None
    application.memo = None
    application_state_transition(application, sc, TriggerEnum.WITHDRAW)
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


@api_router.post(
    "/{id:int}/quote",
    summary="quote insurance application",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_not_shared_link)],
    tags=["brokerage"],
)
async def quote(
    quote_request: QuoteRequest,
    background_tasks: BackgroundTasks,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    sc_core: CoreServiceContext = Security(CoreServiceContext.create),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    if application.operator_id != sc.current_brokerage_user.id:
        raise UnauthorizedError("You are not allowed to quote this application")
    application.sqlmodel_update(quote_request.model_dump(exclude_unset=True))
    application.quote_at = get_current_datetime()
    application_state_transition(application, sc, TriggerEnum.QUOTE)
    title = f"Premium Calculation Completed for Client {application.customer_name} — Please Notify Them Promptly"
    template = "insurance_application_quoted_notification.html"
    _send_notificaiton_email_to_broker(background_tasks, sc, sc_core, application, title, template)
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


@api_router.post(
    "/{id:int}/accept",
    summary="accept quote requst for insurance application",
    tags=["brokerage"],
)
async def accept(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    if application.operator_id is not None and application.operator_id != sc.current_brokerage_user.id:  # type: ignore
        raise UnauthorizedError("This application has already been accepted by another broker")
    if application.status != InsuranceApplicationStatus.QUOTING:
        raise UnauthorizedError("This application is not in pending quote status")
    application.operator_id = sc.current_brokerage_user.id  # type: ignore
    application.operator_name = sc.current_brokerage_user.name
    updated_application = sc.insurance_application_service.update(application)
    return BaseResponse.ok(to_insurance_application_dto(sc, updated_application))


@api_router.post(
    "/{id:int}/unaccept",
    summary="unaccept quote requst for insurance application",
    tags=["brokerage"],
)
async def unaccept(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    if application.operator_id != sc.current_brokerage_user.id:  # type: ignore
        raise UnauthorizedError("This application has not been accepted by you")
    if application.status != InsuranceApplicationStatus.QUOTING:
        raise UnauthorizedError("This application is not in pending quote status")
    application.operator_id = None  # type: ignore
    application.operator_name = None
    application.quote_at = None
    application.underwrite_at = None
    application.memo = None
    updated_application = sc.insurance_application_service.update(application)
    return BaseResponse.ok(to_insurance_application_dto(sc, updated_application))


@api_router.post(
    "/{id:int}/reject",
    summary="quote insurance application",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_not_shared_link)],
    tags=["brokerage"],
)
async def reject(
    rejection_request: RejectionRequest,
    background_tasks: BackgroundTasks,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    sc_core: CoreServiceContext = Security(CoreServiceContext.create),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    if application.operator_id != sc.current_brokerage_user.id:  # type: ignore
        raise UnauthorizedError("You are not allowed to reject this application")
    application.quote_at = None
    application.underwrite_at = None
    application.sqlmodel_update(rejection_request.model_dump(exclude_unset=True))
    application_state_transition(application, sc, TriggerEnum.REJECT)
    title = f"Premium Calculation Request for Client {application.customer_name} Has Been Returned — Please Review"
    template = "insurance_application_quote_returned_notification.html"
    _send_notificaiton_email_to_broker(background_tasks, sc, sc_core, application, title, template)
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


def _send_notificaiton_email_to_broker(
    background_tasks: BackgroundTasks,
    sc: ServiceContext,
    sc_core: CoreServiceContext,
    application: InsuranceApplicationModel,
    title: str,
    template: str,
) -> None:
    broker = sc.broker_service.get_broker_by_id(application.broker_id)  # type: ignore
    template_context = {
        "customer_name": application.customer_name,
        "broker_name": broker.name,
        "ca_broker_url": settings.BETHUNE_SITE_URL,
    }
    background_tasks.add_task(
        sc_core.email_service.send_email,
        subject=title,
        recipients=[broker.user.email],
        template_name=template,
        template_context=template_context,
    )


@api_router.post(
    "/{id:int}/underwritten",
    summary="mark insurance application as underwritten",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker", "brokerage"],
)
async def underwritten(
    underwritten_request: UnderwrittenRequest,
    background_tasks: BackgroundTasks,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    sc_core: CoreServiceContext = Security(CoreServiceContext.create),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    if sc.current_user.is_broker_support_role or sc.current_user.is_brokerage_admin_role:
        if application.operator_id != sc.current_brokerage_user.id:
            raise UnauthorizedError("You are not allowed to underwrite this application")
    application.sqlmodel_update(underwritten_request.model_dump(exclude_unset=True))
    application.underwrite_at = get_current_datetime()
    application_state_transition(application, sc, TriggerEnum.UNDERWRITE)
    if sc.current_user.is_broker_support_role or sc.current_user.is_brokerage_admin_role:
        title = f"Policy Successfully Issued for Client {application.customer_name} — Please Review"
        template = "insurance_application_underwritten_notification.html"
        _send_notificaiton_email_to_broker(background_tasks, sc, sc_core, application, title, template)
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


@api_router.post(
    "/{id:int}/lead-payment",
    summary="mark insurance application as lead payment",
    response_model=BaseResponse[InsuranceApplication],
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker"],
)
async def lead_payment(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[InsuranceApplication]:
    if not sc.insurance_application_service.is_lead_application(application):  # type: ignore
        raise UnauthorizedError("This application is not a lead application")
    if application.status != InsuranceApplicationStatus.UNDERWRITTEN:
        raise UnauthorizedError("This application is not in underwritten status")
    if not sc.current_user.is_broker_role:
        raise UnauthorizedError("You are not allowed to do this action")

    lead_application = sc.lead_service.get_by_application_id(application.id)  # type: ignore
    lead = sc.lead_service.get_by_id(Lead, lead_application.lead_id)  # type: ignore
    sc.lead_service.pay_for_referral_fee_payment(
        sc.current_broker,
        lead,
        application,
    )
    return BaseResponse.ok(to_insurance_application_dto(sc, application))


@api_router.get(
    "",
    summary="get broker's insurance applications",
    response_model=BaseResponse[Pagination[InsuranceApplicationList]],
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker", "brokerage"],
)
async def _(
    query_params: Annotated[InsuranceApplicationQuery, Query()],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
) -> BaseResponse[Pagination[InsuranceApplicationList]]:
    filters = InsuranceApplicationQueryFilters(
        **query_params.model_dump(
            exclude={"page_no", "page_size", "status_group", "email"},
            exclude_unset=True,
        ),
        email=(query_params.email.strip().lower() if query_params.email else query_params.email),
    )
    if sc.current_user.is_broker_role:
        filters.broker_id = sc.current_broker.id  # type: ignore
        filters.status_group = to_broker_view_statuses(query_params.status_group)
    elif sc.current_user.is_broker_support_role or sc.current_user.is_brokerage_admin_role:
        filters.brokerage_id = sc.current_brokerage_user.brokerage_id
        filters.status_group = to_brokerage_view_statuses(query_params.status_group)
    else:
        # should never happen, but just in case
        raise UnauthorizedError("You are not allowed to view insurance applications")

    total, application_models = sc.insurance_application_service.get_by_query_filters(
        filters,
        offset=query_params.offset(),
        limit=query_params.limit(),
    )
    applications = InsuranceApplicationList.from_models(application_models)
    if applications:
        applications = enrich_lead_info(sc, applications)
        applications = enrich_broker_info(sc, applications)

    data = Pagination[InsuranceApplicationList].from_items(
        applications,
        total,
        query_params.page_no,
        query_params.page_size,
    )
    return BaseResponse.ok(data)


@api_router.get(
    "/{id:int}",
    summary="get insurance application by id",
    response_model=BaseResponse[dict],
    dependencies=[Depends(check_shared_link_permission)],
    tags=["broker", "brokerage"],
)
async def _(
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
) -> BaseResponse[dict]:
    dto = to_insurance_application_dto(sc, application)
    data = get_application_json(dto, sc)
    return BaseResponse.ok(data)


@api_router.get(
    "/{ref_code:str}",
    summary="get insurance application by ref_code",
    response_model=BaseResponse[dict],
    dependencies=[Depends(check_has_cached_application_id)],
    tags=["broker", "brokerage"],
)
async def get_by_ref_code(
    ref_code: Annotated[str, Path(description="Insurance application reference code")],
    serial_number: Annotated[str | None, Depends(extract_serial_number)],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
) -> BaseResponse[dict]:
    application = sc.insurance_application_service.get_by_ref_code(ref_code)
    if serial_number is not None:
        if application.serial_number != serial_number:
            raise UnauthorizedError("You are not allowed to view this insurance application")
    if sc.current_user.is_broker_role and application.broker_id != sc.current_broker.id:
        raise UnauthorizedError("You are not allowed to view this insurance application")
    if (
        sc.current_user.is_broker_support_role or sc.current_user.is_brokerage_admin_role
    ) and application.brokerage_id != sc.current_brokerage_user.brokerage_id:
        raise UnauthorizedError("You are not allowed to view this insurance application")
    dto = to_insurance_application_dto(sc, application)
    data = get_application_json(dto, sc)
    return BaseResponse.ok(data)


def get_application_json(insurance_application: InsuranceApplication, sc: ServiceContext) -> dict[str, Any]:
    data = sc.insurance_application_service.read_json(
        insurance_application.insurance_type, insurance_application.serial_number
    )
    data["insurance_application"] = insurance_application.model_dump(mode="json")
    if lead_application := sc.lead_service.get_by_application_id(insurance_application.id):  # type: ignore
        lead = sc.lead_service.get_by_id(Lead, lead_application.lead_id)
        referral_broker = sc.broker_service.get_broker_by_id(lead.created_by)
        lead_application_info: dict[str, Any] = {
            "referral_broker_id": referral_broker.id,
            "referral_broker_name": referral_broker.name,
            "referral_broker_phone": referral_broker.phone,
            "is_anonymous": lead.is_anonymous,
        }
        lead_application_info.update(
            {
                "referral_fee_amount": wiith(
                    sc.lead_service.get_referral_fee_payment(lead.id),  # type: ignore
                    lambda referral_fee_payment: (
                        referral_fee_payment.payment_amount_in_cents / 100 if referral_fee_payment else None
                    ),
                ),
                "referral_fee_type": lead.referral_fee_type,
                "referral_fee_value": lead.referral_fee_value,
            }
        )
        data["lead_application_info"] = lead_application_info
    return data


@api_router.post(
    "/{id:int}/e-docs/{edoc_type}",
    dependencies=[Depends(check_not_shared_link)],
    summary="upload e-doc file for insurance application",
    tags=["broker", "brokerage"],
)
async def upload_edoc(
    serial_number: Annotated[str, Depends(get_serial_number)],
    edoc_type: Annotated[EdocType, Path(description="e-doc file type")],
    file: Annotated[UploadFile, File(description="e-doc file")],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
) -> BaseResponse[dict[str, str]]:
    upload_file = await EdocFileHelper.save(serial_number, edoc_type, file)
    return BaseResponse.ok(upload_file)


@api_router.get(
    "/{id:int}/e-docs/files",
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker", "brokerage"],
)
async def get_edocs(
    serial_number: Annotated[str, Depends(get_serial_number)],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
) -> BaseResponse[dict[EdocType, list[str]]]:
    edoc_files = EdocFileHelper.list_files(serial_number)
    return BaseResponse.ok(edoc_files)


@api_router.get(
    "/{id:int}/e-docs/{edoc_type}/{filename}",
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker", "brokerage"],
)
async def get_edoc(
    serial_number: Annotated[str, Depends(get_serial_number)],
    edoc_type: Annotated[EdocType, Path(description="e-doc file type")],
    filename: Annotated[str, Path(description="e-doc file name")],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
):
    return FileResponse(
        EdocFileHelper.full_path(serial_number, edoc_type, filename),
        filename=filename,
        media_type="application/octet-stream",
    )


@api_router.delete(
    "/{id:int}/e-docs/{edoc_type}/{filename}",
    dependencies=[Depends(check_shared_link_permission)],
    tags=["broker", "brokerage"],
)
async def delete_edoc(
    serial_number: Annotated[str, Depends(get_serial_number)],
    edoc_type: Annotated[EdocType, Path(description="e-doc file type")],
    filename: Annotated[str, Path(description="e-doc file name")],
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:update"]),
) -> BaseResponse[None]:

    EdocFileHelper.delete(
        uid=serial_number,
        edoc_type=edoc_type,
        filename=filename,
    )
    return BaseResponse.ok(None)


@api_router.post(
    "/{id:int}/pdf",
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker", "brokerage"],
)
async def export(
    export_pdf_request: ExportPdfRequest,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
    application: InsuranceApplicationModel = Depends(load_and_check_application),
    # token: str = Header(..., alias="Authorization"),
):
    data = {
        "url": (
            None,
            export_pdf_request.source_url_path,
        ),
        # "extraHttpHeaders": (None, '{"Authorization": "' + token + '"}'),
    }
    if export_pdf_request.wait_delay:
        data.update({"waitDelay": (None, f"{export_pdf_request.wait_delay}s")})
    if export_pdf_request.wait_expression:
        data.update({"waitForExpression": (None, export_pdf_request.wait_expression)})
    # file_name = export_pdf_request.file_name or f"application_{application.id}"
    file_name = f"application_{application.id}"
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            f"{settings.BETHUNE_EXPORT_SERVICE_URL}/forms/chromium/convert/url",
            files=data,
            headers={
                "Gotenberg-Output-Filename": f"{file_name}.pdf",
            },
        )
        response.raise_for_status()

    return StreamingResponse(
        response.iter_bytes(),
        media_type=response.headers.get("content-type", "application/pdf"),
        headers={
            "Content-Disposition": response.headers.get("content-disposition"),
            "Content-Type": response.headers.get("content-type"),
            "Content-Length": response.headers.get("content-length"),
        },
    )


@api_router.post(
    "/{id:int}/share-link",
    dependencies=[Depends(check_not_shared_link)],
    tags=["broker"],
)
async def create_share_link(
    id: int,
    share_link_request: ShareLinkRequest,
    background_tasks: BackgroundTasks,
    sc: ServiceContext = Security(ServiceContext.create, scopes=["insurance:insurance-application:view"]),
    core_sc: CoreServiceContext = Security(CoreServiceContext.create, scopes=["insurance:insurance-application:view"]),
):
    application = sc.insurance_application_service.get_by_id(id)
    if application.broker_id != sc.current_broker.id:
        raise UnauthorizedError("You are not allowed to share this application")
    # cache the share link for 24 hours
    assert application.serial_number is not None
    await get_redis().setex(
        share_link_cache_key(application.serial_number),
        24 * 60 * 60,
        str(application.id),
    )

    now = datetime.now(UTC)
    data = TokenData(
        sub=sc.current_user.email,
        iat=now,
        exp=now + timedelta(minutes=24 * 60),
        jti=str(uuid.uuid4()),
        scopes="insurance:insurance-application:view;insurance:insurance-application:update",
        data_scopes={"insurance_application_serial_number": application.serial_number},
    )
    broker = sc.current_broker
    user = sc.current_user
    shared_link = (
        f"{settings.BETHUNE_SITE_URL}"
        f"?shared=true&name={broker.name}&email={user.email}&phone={broker.phone}"
        f"&token={data.encode()}&type=Bearer"
    )
    shared_link = url_quote(shared_link, safe=":/?&=;") + f"#/insure?ref_code={application.ref_code}"
    await get_redis().setex(
        f"bethune:insurance_application:short_share_link:{application.serial_number}",
        24 * 60 * 60,
        shared_link,
    )
    api_prefix = concat_path(settings.API_PREFIX, settings.API_VERSION, "share-link", "ia")
    short_link = f"{settings.BETHUNE_SITE_URL}{api_prefix}/{application.serial_number}"

    if share_link_request and share_link_request.invite_by_email:
        template_context = {
            "application_shared_link": short_link,
            "broker": sc.current_broker,
            "application": application,
            "broker_email": sc.current_user.email,
        }

        _insurance_type = application.insurance_type.value.lower()
        background_tasks.add_task(
            core_sc.email_service.send_email,
            subject="Insurance Application Invitation",
            recipients=[application.email],
            template_name=f"{_insurance_type}_application_invitation.html",
            template_context=template_context,
        )
    return BaseResponse.ok({"shared_link": short_link})


def __filter_route_by_tag(tag: str, new_tags: list[str] | None = None) -> APIRouter:
    new_api_router = APIRouter(
        prefix="",
        tags=new_tags if new_tags else api_router.tags,
        dependencies=api_router.dependencies,
    )
    for route in api_router.routes:
        if tag in route.tags:
            new_api_router.add_api_route(
                path=route.path,
                endpoint=route.endpoint,
                methods=route.methods,
                response_model=route.response_model,
                summary=route.summary,
                description=route.description,
                dependencies=route.dependencies,
            )
    return new_api_router


api_router_for_broker = __filter_route_by_tag("broker", ["broker-insurance-application"])
api_router_for_brokerage = __filter_route_by_tag("brokerage", ["brokerage-insurance-application"])
