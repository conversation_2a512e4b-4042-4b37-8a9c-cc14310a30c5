from typing import Annotated

from fastapi import APIRouter
from fastapi import Depends
from fastapi import Header
from sqlalchemy.exc import NoResultFound

from ...dto.auth import LoginRequestDTO
from ...dto.base import LoginTypeEnum
from ..system.service_context import ServiceContext
from bethune.api.dto.auth import Token
from bethune.api.dto.base import BaseResponse
from bethune.api.endpoint.insurance.service_context import ServiceContext as InsuranceServiceContext
from bethune.api.endpoint.system.service_context import ServiceContext as SystemServiceContext
from bethune.api.error import UnauthenticatedError
from bethune.error import NotFoundError
from bethune.model import User as UserModel

api_router = APIRouter(tags=["auth"])


@api_router.post(
    "/login",
    response_model=BaseResponse[Token],
    description="It returns BaseResponse Object just as other endpoints.",
)
async def _login(
    sc: Annotated[ServiceContext, Depends()],
    sc_insurance: Annotated[InsuranceServiceContext, Depends()],
    login_data: LoginRequestDTO,
):  # type: ignore
    user: UserModel = sc.auth_service.authenticate_user(login_data.username, login_data.password)
    if login_data.type == LoginTypeEnum.MOBILE:
        if user.is_broker_support_role or user.is_brokerage_admin_role:
            # 经纪行内勤和管理员不让从这登录
            raise UnauthenticatedError("this type of user cannot log on this site")
        return BaseResponse[Token].ok(sc.auth_service.create_token(login_data.username, login_data.password))
    if login_data.type == LoginTypeEnum.PC:
        try:
            sc_insurance.brokerage_service.get_by_user_id(user.id)  # type: ignore
            if user.is_broker_role or user.is_referral_broker_role:
                raise UnauthenticatedError(err_msg="this type of user cannot log on this site")
        except (NoResultFound, NotFoundError):
            raise UnauthenticatedError(err_msg="user is not a brokerage user")
        return BaseResponse[Token].ok(sc.auth_service.create_token(login_data.username, login_data.password))
    return BaseResponse[Token].ok(sc.auth_service.create_token(login_data.username, login_data.password))


@api_router.post(
    "/logout",
    summary="logout broker",
    response_model=BaseResponse[None],
)
async def logout(
    sc: Annotated[SystemServiceContext, Depends()],
    authorization: str = Header(..., description="Bearer token"),
) -> BaseResponse[None]:
    """Logout endpoint. Invalidates the access token."""
    token = authorization.split(" ")[1]
    await sc.auth_service.logout(token)
    return BaseResponse.ok(None)
