import logging
import gettext
from functools import lru_cache
from pathlib import Path
from typing import Callable

from babel.core import Locale
from babel.support import Translations
from fastapi_babel.local_context import context_var
from bethune.settings import settings


def get_text(raw_message) -> str:
    """
    获取原始消息的本地化文本
    :param raw_message 原始消息
    :return 本地化消息
    """
    try:
        return context_var.get()(raw_message)
    except LookupError:
        logging.warning(f"Can't find message: {raw_message}")
        return raw_message


@lru_cache(maxsize=32)
def _load_translation(language: str) -> Translations:
    """
    使用Babel加载指定语言的翻译文件（缓存优化）
    :param language: 语言代码，如 'zh', 'en', 'fr'
    :return: Babel翻译对象
    """
    locale_dir = Path(settings.I18N_LOCALE_DIR)
    try:
        # 使用Babel的Translations类加载翻译
        locale = Locale.parse(language)
        translation = Translations.load(
            dirname=locale_dir,
            locales=[locale],
            domain="messages"
        )
        return translation
    except Exception as e:
        logging.warning(f"Translation file not found for language: {language}, error: {e}")
        # 返回空的Babel翻译对象
        return Translations()


def create_translator(language: str) -> Callable[[str], str]:
    """
    创建指定语言的翻译器（柯里化函数）
    用于非HTTP环境，如定时任务、后台任务等

    :param language: 语言代码，如 'zh', 'en', 'fr'
    :return: 翻译函数

    使用示例:
        zh_translator = create_translator('zh')
        message = zh_translator('InboxMessage_PolicyRenewalReminder')
    """
    translation = _load_translation(language)

    def translate(msgid: str) -> str:
        """
        翻译指定的msgid
        :param msgid: 要翻译的消息ID
        :return: 翻译后的文本
        """
        try:
            # Babel的Translations对象使用ugettext方法
            result = translation.ugettext(msgid)
            return result if result != msgid else msgid
        except Exception as e:
            logging.warning(f"Translation failed for {msgid} in {language}: {e}")
            return msgid

    return translate
