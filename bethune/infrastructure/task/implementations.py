from abc import ABC
from abc import abstractmethod
from typing import Any

from bethune.api.dto.reminder import ReminderMessageCreate
from bethune.db.session_context import session_context
from bethune.infrastructure.email.email_client import EmailClient
from bethune.infrastructure.task.messages import InsuranceReminderMessage
from bethune.infrastructure.task.messages import ReminderType
from bethune.logging import logger
from bethune.service.insurance.factory import ServiceFactory as ServiceInsuranceFactory
from bethune.service.reminder.factory import ServiceFactory as ServiceReminderFactory


class BaseTask(ABC):
    def __init__(self, task_data: dict[str, Any]):
        self.task_data = task_data

    @abstractmethod
    async def run(self):
        """执行任务的具体逻辑"""


class EmailTask(BaseTask):
    def __init__(self, task_data: dict[str, Any]):
        super().__init__(task_data)
        self.email_client = EmailClient()

    async def run(self):
        """Send email with provided parameters"""
        logger.info(f"Processing email task: {self.task_data['task_id']}")

        try:

            await self.email_client.send_email(
                subject=self.task_data["subject"],
                recipients=self.task_data["recipients"],
                template_name=self.task_data["template_name"],
                template_context=self.task_data["template_context"],
            )
            logger.info(f"Successfully sent email for task {self.task_data['task_id']}")
        except Exception as e:
            logger.error(f"Failed to send email for task {self.task_data['task_id']}: {e}")


class InboxTask(BaseTask):
    """Base task for sending internal messages"""

    def __init__(self, task_data):
        super().__init__(task_data)
        self.reminder_message_service = ServiceReminderFactory.create_reminder_message_service()
        self.broker_service = ServiceInsuranceFactory.create_broker_service()

    async def run(self):
        """Send internal message with provided parameters"""
        if self.task_data["reminder_type"] == ReminderType.CUSTOMER.value:
            logger.warning("Customer reminder type is not supported for inbox task")
        else:
            logger.info(f"Processing inbox task: {self.task_data['task_id']}")

            try:
                with session_context():
                    reminder_message = ReminderMessageCreate(
                        **{
                            "business_type": self.task_data["business_type"],
                            "insurance_type": self.task_data["insurance_type"],
                            "content": self.task_data["content"],
                            "extra_content": self.task_data["extra_content"],
                            "receiver_id": self.task_data["receiver_id"],
                            "sender_id": self.task_data.get("sender_id", 0),  # Default to system message
                            "context": self.task_data.get("context"),
                        }
                    )

                    self.reminder_message_service.create(reminder_message.to_model())
                logger.info(f"Successfully created message for task {self.task_data['task_id']}")
            except Exception as e:
                logger.error(f"Failed to create message for task {self.task_data['task_id']}: {e}")


class PolicyRenewalEmailTask(EmailTask):
    """Specialized email task for insurance policy renewal reminders"""

    def __init__(self, task_data: dict):
        super().__init__(task_data)
        self.email_message = InsuranceReminderMessage(**self.task_data)

    async def run(self):
        logger.info(f"Processing policy renewal email task: {self.task_data['task_id']}")
        email_message = self.email_message.to_email_message()
        # Set email parameters in task data
        self.task_data.update(
            **{
                "subject": email_message.subject,
                "recipients": email_message.recipients,
                "template_name": email_message.template_name,
                "template_context": email_message.template_context,
            }
        )

        # Delegate to parent EmailTask for actual sending
        await super().run()


class PolicyRenewalInboxTask(InboxTask):
    """Specialized inbox task for insurance policy renewal reminders"""

    def __init__(self, task_data):
        super().__init__(task_data)
        self.inbox_message = InsuranceReminderMessage(**self.task_data)

    async def run(self):
        logger.info(f"Processing policy renewal inbox task: {self.task_data['task_id']}")

        # Prepare message details
        with session_context():
            inbox_message = self.inbox_message.to_inbox_message()
            self.task_data.update(
                **{
                    "content": inbox_message.content,
                    "extra_content": inbox_message.extra_content,
                    "receiver_id": inbox_message.receiver_id,
                    "context": inbox_message.context
                }
            )

        # Delegate to parent InboxTask for actual sending
        await super().run()


class SmsTask(BaseTask):
    async def run(self):
        logger.info(f"Processing sms task: {self.task_data['task_id']}")
        # 实现短信逻辑
