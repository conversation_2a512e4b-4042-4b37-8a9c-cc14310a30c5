from .broker import BrokerRepository
from .broker import InsuranceConsultationApplicationRepository
from .broker import InsuranceConsultationCustomerRepository
from .broker import InsuranceConsultationRepository
from .broker import PromotionMaterialRepository
from .brokerage import BrokerageRepository
from .customer import CustomerRepository
from .insurance_application import InsuranceApplicationRepository
from .insurance_company import InsuranceCompanyRepository
from .insurance_policy import InsurancePolicyRepository
from .lead import LeadRepository
from .user_feedback import UserFeedbackRepository


class InsuranceRepositoryFactory:

    @staticmethod
    def create_insurance_company_repository() -> InsuranceCompanyRepository:
        return InsuranceCompanyRepository()

    @staticmethod
    def create_customer_repository() -> CustomerRepository:
        return CustomerRepository()

    @staticmethod
    def create_broker_repository() -> BrokerRepository:
        return BrokerRepository()

    @staticmethod
    def create_lead_repository() -> LeadRepository:
        return LeadRepository()

    @staticmethod
    def create_insurance_application_repository() -> InsuranceApplicationRepository:
        return InsuranceApplicationRepository()

    @staticmethod
    def create_insurance_policy_repository() -> InsurancePolicyRepository:
        return InsurancePolicyRepository()

    @staticmethod
    def create_user_feedback_repository() -> UserFeedbackRepository:
        return UserFeedbackRepository()

    @staticmethod
    def create_brokerage_repository() -> BrokerageRepository:
        return BrokerageRepository()

    @staticmethod
    def create_insurance_consultation_repository() -> InsuranceConsultationRepository:
        return InsuranceConsultationRepository()

    @staticmethod
    def create_promotion_material_repository() -> PromotionMaterialRepository:
        return PromotionMaterialRepository()

    @staticmethod
    def create_insurance_consultation_application_repository() -> InsuranceConsultationApplicationRepository:
        return InsuranceConsultationApplicationRepository()

    @staticmethod
    def create_insurance_consultation_customer_repository() -> InsuranceConsultationCustomerRepository:
        return InsuranceConsultationCustomerRepository()
