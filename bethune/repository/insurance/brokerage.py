from collections.abc import Iterable

from sqlalchemy.exc import NoResultFound
from sqlmodel import col
from sqlmodel import func
from sqlmodel import select
from sqlmodel import update
from sqlmodel.sql.expression import label
from sqlmodel.sql.expression import Select
from sqlmodel.sql.expression import SelectOfScalar

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model import Broker
from bethune.model import User
from bethune.model import UserStatus
from bethune.model.brokerage import Brokerage
from bethune.model.brokerage_user import BrokerageUser
from bethune.model.brokerage_user import BrokerageUserQueryFilters
from bethune.model.brokerage_user import BrokerageUserRichInfoComposite
from bethune.model.system import Role
from bethune.model.system import UserRole
from bethune.repository import DEFAULT_LIMIT
from bethune.repository.base import GenericRepository


def _add_query_conditions(filters: BrokerageUserQueryFilters, stmt: Select | SelectOfScalar) -> Select | SelectOfScalar:
    stmt = (
        stmt.join(User, BrokerageUser.user_id == User.id)
        .join(UserRole, User.id == UserRole.user_id)
        .join(Role, UserRole.role_id == Role.id)
        .outerjoin(Broker, BrokerageUser.user_id == Broker.user_id)
    )

    if filters.name:
        stmt = stmt.where(col(BrokerageUser.name).ilike(f"%{filters.name}%"))
    if filters.email:
        stmt = stmt.where(col(User.email).ilike(f"%{filters.email}%"))

    return stmt.where(BrokerageUser.brokerage_id == filters.brokerage_id, Role.name == filters.role)


class BrokerageRepository(GenericRepository):
    def get_by_broker_id(self, broker_id: int):
        try:
            return session().exec(select(Brokerage).where(Brokerage.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerProfile not found", detail={"broker_id": broker_id})

    def get_by_user_id(self, user_id: int, allow_none: bool = False) -> BrokerageUser | None:
        try:
            return session().exec(select(BrokerageUser).where(BrokerageUser.user_id == user_id)).one()
        except NoResultFound:
            if allow_none:
                return None
            raise NotFoundError("BrokerageUser not found", detail={"user_id": user_id})

    def get_by_query_filters(
        self,
        filters: BrokerageUserQueryFilters,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[BrokerageUser]]:
        """
        Get broker users by query filters.
        """
        count_stmt = _add_query_conditions(filters, select(func.count(BrokerageUser.id)))
        total = session().exec(count_stmt).one()

        stmt = (
            _add_query_conditions(filters, select(BrokerageUser, label("broker_id", Broker.id)))
            .order_by(col(BrokerageUser.created_at).desc())
            .limit(limit)
            .offset(offset)
        )

        results = [
            BrokerageUserRichInfoComposite(
                **brokerage_user.model_dump(),
                user=brokerage_user.user,
                brokerage=brokerage_user.brokerage,
                broker_id=broker_id,
            )
            for brokerage_user, broker_id in session().exec(stmt).all()
        ]

        return total, results

    def validate_brokerage_user_ids(self, ids: Iterable[int], brokerage_id: int) -> bool:  # 接受任何可迭代的整数集合
        """
        验证给定的人员ID集合是否都属于指定的经纪公司

        :param ids: 要验证的人员ID集合(可迭代对象)
        :param brokerage_id: 经纪公司ID
        :return: 是否都是有效id
        """
        # 将输入转换为集合(自动去重)
        input_ids = set(ids)

        if not input_ids:
            return True

        # 查询属于指定经纪公司的ID
        stmt = select(BrokerageUser.id).where(
            col(BrokerageUser.id).in_(input_ids), BrokerageUser.brokerage_id == brokerage_id
        )
        valid_ids = set(session().exec(stmt).all())

        # 返回无效ID集合
        return not (input_ids - valid_ids)

    def batch_update_enabled_status(self, ids: Iterable[int], enabled: bool, brokerage_id: int) -> int:
        """
        批量更新指定ID集合的人员启用状态

        :param ids: 要更新的人员ID集合
        :param enabled: 目标启用状态
        :param brokerage_id: 经纪公司ID
        :return: 更新的记录数量
        """
        if not ids:
            return 0

        # 查询符合条件的BrokerageUser，获取对应的user_id
        stmt = select(BrokerageUser.user_id).where(
            col(BrokerageUser.id).in_(ids), BrokerageUser.brokerage_id == brokerage_id
        )
        user_ids = session().exec(stmt).all()

        if not user_ids:
            return 0

        new_status = UserStatus.ACTIVE if enabled else UserStatus.INACTIVE

        # 构建批量更新语句
        stmt = update(User).where(col(User.id).in_(user_ids)).values(status=new_status)

        # 执行更新
        result = session().exec(stmt)

        # 返回更新的记录数
        return result.rowcount
