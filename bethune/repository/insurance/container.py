from dependency_injector import containers
from dependency_injector import providers

from bethune.repository.insurance.broker import BrokerRepository
from bethune.repository.insurance.broker import InsuranceConsultationApplicationRepository
from bethune.repository.insurance.broker import InsuranceConsultationCustomerRepository
from bethune.repository.insurance.broker import InsuranceConsultationRepository
from bethune.repository.insurance.broker import PromotionMaterialRepository
from bethune.repository.insurance.brokerage import BrokerageRepository
from bethune.repository.insurance.customer import CustomerRepository
from bethune.repository.insurance.insurance_application import InsuranceApplicationRepository
from bethune.repository.insurance.insurance_company import InsuranceCompanyRepository
from bethune.repository.insurance.insurance_policy import InsurancePolicyRepository
from bethune.repository.insurance.lead import LeadRepository
from bethune.repository.insurance.user_feedback import UserFeedbackRepository


class InsuranceRepositoryContainer(containers.DeclarativeContainer):
    """Container for insurance repository instances."""

    insurance_company_repository = providers.Factory(InsuranceCompanyRepository)
    customer_repository = providers.Factory(CustomerRepository)
    broker_repository = providers.Factory(BrokerRepository)
    lead_repository = providers.Factory(LeadRepository)
    insurance_application_repository = providers.Factory(InsuranceApplicationRepository)
    insurance_policy_repository = providers.Factory(InsurancePolicyRepository)
    user_feedback_repository = providers.Factory(UserFeedbackRepository)
    brokerage_repository = providers.Factory(BrokerageRepository)
    insurance_consultation_repository = providers.Factory(InsuranceConsultationRepository)
    promotion_material_repository = providers.Factory(PromotionMaterialRepository)
    insurance_consultation_application_repository = providers.Factory(InsuranceConsultationApplicationRepository)
    insurance_consultation_customer_repository = providers.Factory(InsuranceConsultationCustomerRepository)
