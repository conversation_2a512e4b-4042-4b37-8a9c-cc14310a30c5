from sqlalchemy.exc import NoResultFound
from sqlmodel import case
from sqlmodel import col
from sqlmodel import delete
from sqlmodel import func
from sqlmodel import select
from sqlmodel import update
from sqlmodel.sql.expression import label
from sqlmodel.sql.expression import Select
from sqlmodel.sql.expression import SelectOfScalar

from bethune.db.session_context import session
from bethune.error.errors import NotFoundError
from bethune.model import Broker
from bethune.model import Brokerage
from bethune.model import BrokerLeadConfig
from bethune.model import BrokerProfile
from bethune.model import BrokerQualification
from bethune.model import User
from bethune.model import UserStatus
from bethune.model.base import ConsultationStatus
from bethune.model.base import InsuranceType
from bethune.model.broker import BrokerLeadFee
from bethune.model.broker import BrokerPaymentMethod
from bethune.model.broker import BrokerRichInfoComposite
from bethune.model.broker import InsuranceConsultation
from bethune.model.broker import InsuranceConsultationApplication
from bethune.model.broker import InsuranceConsultationCustomer
from bethune.model.broker import PromotionMaterial
from bethune.repository.base import BaseRepository
from bethune.repository.base import DEFAULT_LIMIT
from bethune.repository.base import GenericRepository


def _populate_query_lead_candidate_brokers_conditions(
    current_broker: Broker,
    insurance_type: InsuranceType,
    customer_province: str,
    _customer_city: str,
    keyword: str | None,
    statement: Select | SelectOfScalar,
    is_finding_referer_broker=False,
) -> Select | SelectOfScalar:
    basic_statement = (
        statement.join(User, Broker.user_id == User.id)  # 关联用户（为了获取email）
        .join(BrokerProfile, Broker.id == BrokerProfile.broker_id)  # 关联代理人档案
        .join(BrokerLeadConfig, Broker.id == BrokerLeadConfig.broker_id)  # 关联代理人线索配置
        .join(
            BrokerQualification,
            BrokerProfile.id == BrokerQualification.broker_profile_id,
        )  # 关联资质认证
        .outerjoin(BrokerLeadFee, Broker.id == BrokerLeadFee.broker_id)
        .outerjoin(Brokerage, Broker.brokerage_id == Brokerage.id)
        .where(
            User.status == UserStatus.ACTIVE,  # 用户状态为活跃
            col(BrokerQualification.is_qualified).is_(True),  # 过滤已认证条件
            col(BrokerLeadConfig.allow_leads).is_(True),  # 接受线索
            func.FIND_IN_SET(insurance_type, BrokerLeadConfig.insurance_type),  # 保险类型匹配
            current_broker.id != Broker.id,  # 排除自己
            Broker.province == customer_province,  # 省份匹配
            BrokerLeadFee.insurance_type == insurance_type,  # 费用类型匹配
        )
    )
    if is_finding_referer_broker:
        return basic_statement

    return basic_statement.where(col(Broker.name).ilike(f"%{keyword}%")) if keyword else basic_statement


class BrokerRepository(GenericRepository):

    def get_broker_by_id(self, id: int) -> Broker:
        return self.get_by_id(model_class=Broker, id=id)

    def get_broker_by_uid(self, uid: str) -> Broker:
        try:
            return session().exec(select(Broker).where(Broker.uid == uid)).one()
        except NoResultFound:
            raise NotFoundError("Broker not found", detail={"uid": uid})

    def get_broker_by_user_id(self, user_id: int, allow_none: bool = False) -> Broker | None:
        try:
            return session().exec(select(Broker).where(Broker.user_id == user_id)).one()
        except NoResultFound:
            if allow_none:
                return None
            raise NotFoundError("Broker not found", detail={"user_id": user_id})

    def get_broker_by_ids(self, ids: list[int]) -> list[Broker]:
        if not ids:
            return []
        statement = select(Broker).where(col(Broker.id).in_(ids))
        return session().exec(statement).all()

    def get_broker_friends(self, referer_user_id: int, offset: int = 0, limit: int = DEFAULT_LIMIT):
        count_statement = (
            select(func.count())
            .join_from(Broker, User, Broker.user_id == User.id)
            .where(User.referer_id == referer_user_id)
        )
        total = session().exec(count_statement).one()
        if total == 0 or offset >= total:
            return total, []
        statement = (
            select(Broker)
            .join_from(Broker, User, Broker.user_id == User.id)
            .where(User.referer_id == referer_user_id)
            .order_by(col(Broker.created_at).asc())
            .offset(offset)
            .limit(limit)
        )
        return total, session().exec(statement).all()

    def get_lead_candidate_brokers(
        self,
        current_broker: Broker,
        insurance_type: InsuranceType,
        customer_province: str,
        customer_city: str,
        keyword: str | None = None,
        page_no: int = 1,
        _page_size: int = 20,
        offset: int = 0,
        limit: int = DEFAULT_LIMIT,
    ) -> tuple[int, list[BrokerRichInfoComposite], BrokerRichInfoComposite | None]:
        """获取以推荐者优先的有资质代理人列表"""
        user = session().get(User, current_broker.user_id)
        referer_user_id = user.referer_id
        referer_broker = None
        if referer_user_id:
            result = (
                session()
                .exec(
                    _populate_query_lead_candidate_brokers_conditions(
                        current_broker,
                        insurance_type,
                        customer_province,
                        customer_city,
                        keyword,
                        select(
                            Broker,
                            User.email,
                            User.avatar,
                            BrokerLeadFee,
                            label("brokerage_name", Brokerage.name),
                        ),
                        True,
                    ).where(User.id == referer_user_id)
                )
                .one_or_none()
            )
            if result:
                broker, email, avatar, lead_fee, brokerage_name = result
                referer_broker = BrokerRichInfoComposite(
                    **broker.model_dump(),
                    email=email,
                    avatar=avatar,
                    profile_public_fields=broker.profile.public_fields,
                    lead_config_referral_fee_type=(lead_fee.referral_fee_type if lead_fee else None),
                    lead_config_referral_fee_value=(lead_fee.referral_fee_value if lead_fee else None),
                    lead_config_willing_pay_for_leads=broker.lead_config.willing_pay_for_leads,
                    brokerage_name=brokerage_name,
                )

        count_statement = _populate_query_lead_candidate_brokers_conditions(
            current_broker,
            insurance_type,
            customer_province,
            customer_city,
            keyword,
            select(func.count(Broker.id)),
        )
        query_statement = (
            _populate_query_lead_candidate_brokers_conditions(
                current_broker,
                insurance_type,
                customer_province,
                customer_city,
                keyword,
                select(
                    Broker,
                    User.email,
                    User.avatar,
                    BrokerLeadFee,
                    label("brokerage_name", Brokerage.name),
                ),
            )
            .limit(limit)
            .offset(offset)
        )

        if referer_broker:
            # 使用 CASE 表达式实现推荐人优先排序
            order_condition = case((Broker.id == referer_broker.id, 0), else_=1).asc()  # 推荐人优先级最高
            ordered_query = query_statement.order_by(
                order_condition,
                col(BrokerLeadFee.referral_fee_value).desc(),
                Broker.created_at,
            )
        else:
            ordered_query = query_statement.order_by(col(BrokerLeadFee.referral_fee_value).desc(), Broker.created_at)

        total = session().exec(count_statement).one()

        brokers = [
            BrokerRichInfoComposite(
                **broker.model_dump(),
                email=email,
                avatar=avatar,
                profile_public_fields=broker.profile.public_fields,
                lead_config_referral_fee_type=(lead_fee.referral_fee_type if lead_fee else None),
                lead_config_referral_fee_value=(lead_fee.referral_fee_value if lead_fee else None),
                lead_config_willing_pay_for_leads=broker.lead_config.willing_pay_for_leads,
                brokerage_name=brokerage_name,
            )
            for broker, email, avatar, lead_fee, brokerage_name in session().exec(ordered_query).all()
        ]

        if referer_broker and referer_broker not in brokers:
            brokers = [referer_broker] + brokers  # 确保分页数量一致

        return (
            total + (1 if referer_broker and page_no == 1 else 0),
            brokers,
            referer_broker,
        )

    def get_lead_config_by_broker_id(self, broker_id: int) -> BrokerLeadConfig:
        try:
            return session().exec(select(BrokerLeadConfig).where(BrokerLeadConfig.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerLeadConfig not found", detail={"broker_id": broker_id})

    def get_profile_by_broker_id(self, broker_id: int) -> BrokerProfile:
        try:
            return session().exec(select(BrokerProfile).where(BrokerProfile.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerProfile not found", detail={"broker_id": broker_id})

    def get_payment_method_by_broker_id(self, broker_id: int) -> BrokerPaymentMethod:
        try:
            return session().exec(select(BrokerPaymentMethod).where(BrokerPaymentMethod.broker_id == broker_id)).one()
        except NoResultFound:
            raise NotFoundError("BrokerPaymentMethod not found", detail={"broker_id": broker_id})

    def get_qualification_by_broker_id(self, broker_id: int) -> BrokerQualification:
        try:
            return (
                session()
                .exec(
                    select(BrokerQualification)
                    .where(BrokerQualification.broker_profile_id == BrokerProfile.id)
                    .where(BrokerProfile.broker_id == broker_id)
                )
                .one()
            )
        except NoResultFound:
            raise NotFoundError("BrokerQualification not found", detail={"broker_id": broker_id})

    def get_lead_fee_by_broker_id(self, broker_id: int) -> list[BrokerLeadFee]:
        return session().exec(select(BrokerLeadFee).where(BrokerLeadFee.broker_id == broker_id)).all()

    def get_lead_fee_by_broker_id_and_insurance_type(
        self, broker_id: int, insurance_type: InsuranceType
    ) -> BrokerLeadFee | None:
        return (
            session()
            .exec(
                select(BrokerLeadFee).where(
                    BrokerLeadFee.broker_id == broker_id,
                    BrokerLeadFee.insurance_type == insurance_type,
                )
            )
            .one_or_none()
        )

    def delete_lead_fee_by_broker_id(self, broker_id: int):
        stmt = delete(BrokerLeadFee).where(
            BrokerLeadFee.broker_id == broker_id,
        )
        session().exec(stmt)
        session().flush()

    def create_lead_fees(self, lead_fees: list[BrokerLeadFee]) -> list[BrokerLeadFee]:
        session().add_all(lead_fees)
        session().flush()
        return lead_fees


class PromotionMaterialRepository(BaseRepository[PromotionMaterial]):

    def __init__(self):
        super().__init__(model_class=PromotionMaterial)

    def get_by_broker_and_id(self, broker_id: int, id: int):
        try:
            return (
                session()
                .exec(
                    select(PromotionMaterial)
                    .where(PromotionMaterial.broker_id == broker_id)
                    .where(PromotionMaterial.id == id)
                )
                .one()
            )
        except NoResultFound:
            raise NotFoundError("PromotionMaterial not found", detail={"id": id})


class InsuranceConsultationRepository(BaseRepository[InsuranceConsultation]):

    def __init__(self):
        super().__init__(model_class=InsuranceConsultation)

    def mark_as_completed(self, broker_id: int, id: int):
        stmt = (
            update(InsuranceConsultation)
            .where(InsuranceConsultation.status == ConsultationStatus.PENDING)
            .where(InsuranceConsultation.id == id)
            .where(InsuranceConsultation.broker_id == broker_id)
            .values(**{"status": ConsultationStatus.COMPLETED.value})
        )

        result = session().exec(stmt)
        session().flush()
        return result.rowcount


class InsuranceConsultationApplicationRepository(BaseRepository[InsuranceConsultationApplication]):

    def __init__(self):
        super().__init__(model_class=InsuranceConsultationApplication)


class InsuranceConsultationCustomerRepository(BaseRepository[InsuranceConsultationCustomer]):

    def __init__(self):
        super().__init__(model_class=InsuranceConsultationCustomer)
