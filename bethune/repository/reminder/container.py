from dependency_injector import containers
from dependency_injector import providers

from bethune.repository.reminder.config import ReminderConfigRepository
from bethune.repository.reminder.message import ReminderMessageRepository


class ReminderRepositoryContainer(containers.DeclarativeContainer):
    """Container for reminder repository instances."""

    reminder_config_repository = providers.Factory(ReminderConfigRepository)
    reminder_message_repository = providers.Factory(ReminderMessageRepository)
