from dependency_injector import containers
from dependency_injector import providers

from bethune.repository.system.oauth_user_info import OAuthUserInfoRepository
from bethune.repository.system.permission import PermissionRepository
from bethune.repository.system.role import RoleRepository
from bethune.repository.system.user import UserRepository


class SystemRepositoryContainer(containers.DeclarativeContainer):
    """Container for system repository instances."""

    user_repository = providers.Factory(UserRepository)
    role_repository = providers.Factory(RoleRepository)
    permission_repository = providers.Factory(PermissionRepository)
    oauth_user_info_repository = providers.Factory(OAuthUserInfoRepository)
