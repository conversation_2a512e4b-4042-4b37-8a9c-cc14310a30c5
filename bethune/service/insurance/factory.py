from .broker import BrokerService
from .brokerage import BrokerageService
from .customer import CustomerService
from .insurance_application import InsuranceApplicationService
from .insurance_company import InsuranceCompanyService
from .insurance_consultation import InsuranceConsultationService
from .insurance_policy import InsurancePolicyService
from .lead import LeadService
from .promotion_material import PromotionMaterialService
from bethune.repository.insurance import InsuranceRepositoryContainer
from bethune.service.insurance.insurance_consultation_application import InsuranceConsultationApplicationService
from bethune.service.insurance.insurance_consultation_customer import InsuranceConsultationCustomerService
from bethune.service.insurance.user_feedback import UserFeedbackService


class ServiceFactory:

    @staticmethod
    def create_insurance_company_service() -> InsuranceCompanyService:
        return InsuranceCompanyService(InsuranceRepositoryContainer.insurance_company_repository())

    @staticmethod
    def create_customer_service() -> CustomerService:
        return CustomerService(
            InsuranceRepositoryContainer.customer_repository(),
            InsuranceRepositoryContainer.insurance_application_repository(),
        )

    @staticmethod
    def create_insurance_application_service() -> InsuranceApplicationService:
        return InsuranceApplicationService(
            InsuranceRepositoryContainer.insurance_application_repository(),
            InsuranceRepositoryContainer.lead_repository(),
        )

    @staticmethod
    def create_broker_service() -> BrokerService:
        return BrokerService(
            InsuranceRepositoryContainer.broker_repository(),
            InsuranceRepositoryContainer.brokerage_repository(),
        )

    @staticmethod
    def create_lead_service() -> LeadService:
        return LeadService(InsuranceRepositoryContainer.lead_repository())

    @staticmethod
    def create_insurance_policy_service() -> InsurancePolicyService:
        return InsurancePolicyService(InsuranceRepositoryContainer.insurance_policy_repository())

    @staticmethod
    def create_user_feedback_service() -> UserFeedbackService:
        return UserFeedbackService(InsuranceRepositoryContainer.user_feedback_repository())

    @staticmethod
    def create_brokerage_service() -> BrokerageService:
        return BrokerageService(InsuranceRepositoryContainer.brokerage_repository())

    @staticmethod
    def create_insurance_consultation_service() -> InsuranceConsultationService:
        return InsuranceConsultationService(InsuranceRepositoryContainer.insurance_consultation_repository())

    @staticmethod
    def create_promotion_service() -> PromotionMaterialService:
        return PromotionMaterialService(InsuranceRepositoryContainer.promotion_material_repository())

    @staticmethod
    def create_insurance_consultation_application_service() -> InsuranceConsultationApplicationService:
        return InsuranceConsultationApplicationService(
            InsuranceRepositoryContainer.insurance_consultation_application_repository()
        )

    @staticmethod
    def create_insurance_consultation_customer_service() -> InsuranceConsultationCustomerService:
        return InsuranceConsultationCustomerService(
            InsuranceRepositoryContainer.insurance_consultation_customer_repository()
        )
