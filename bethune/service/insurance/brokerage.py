from collections.abc import Iterable

from fastapi import UploadFile

from bethune.model import BrokerageUser
from bethune.model.brokerage_user import BrokerageUserQueryFilters
from bethune.repository import DEFAULT_LIMIT
from bethune.repository.insurance.brokerage import BrokerageRepository
from bethune.service.base import BaseGenericService
from bethune.settings import settings
from bethune.util.file import upload_file


class BrokerageService(BaseGenericService[BrokerageRepository]):

    async def upload_company_logo(self, file: UploadFile, logo: str | None = None) -> tuple[str, str]:
        return upload_file(file, settings.LOGOS_FOLDER, logo)

    def get_by_user_id(self, user_id: int, allow_none: bool = False) -> BrokerageUser | None:
        """
        Retrieve a BrokerageUser by the user ID.

        :param user_id: The ID of the user.
        :return: A BrokerageUser instance or None if not found.
        """
        return self.repository.get_by_user_id(user_id, allow_none)

    def get_by_query_filters(
        self, filters: BrokerageUserQueryFilters, offset: int = 0, limit: int = DEFAULT_LIMIT
    ) -> tuple[int, list[BrokerageUser]]:
        return self.repository.get_by_query_filters(filters, offset, limit)

    def validate_brokerage_user_ids(self, ids: Iterable[int], brokerage_id: int) -> bool:  # 接受任何可迭代的整数集合
        return self.repository.validate_brokerage_user_ids(ids, brokerage_id)

    def batch_update_enabled_status(self, ids: Iterable[int], enabled: bool, brokerage_id: int) -> int:
        return self.repository.batch_update_enabled_status(ids, enabled, brokerage_id)
