from .auth import AuthService
from .oauth_user_info import OAuthUserInfoService
from .permission import PermissionService
from .role import RoleService
from .user import UserService
from bethune.repository.system import SystemRepositoryContainer


class ServiceFactory:

    @staticmethod
    def create_user_service() -> UserService:
        return UserService(SystemRepositoryContainer.user_repository())

    @staticmethod
    def create_role_service() -> RoleService:
        return RoleService(SystemRepositoryContainer.role_repository())

    @staticmethod
    def create_permission_service() -> PermissionService:
        return PermissionService(SystemRepositoryContainer.permission_repository())

    @staticmethod
    def create_auth_service(user_service: UserService) -> AuthService:
        return AuthService(user_service)

    @staticmethod
    def create_oauth_user_info_service() -> OAuthUserInfoService:
        return OAuthUserInfoService(SystemRepositoryContainer.oauth_user_info_repository())
