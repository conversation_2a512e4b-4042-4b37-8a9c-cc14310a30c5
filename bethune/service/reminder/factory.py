from .config import ReminderConfigService
from .message import <PERSON>minderMessageService
from bethune.repository.reminder import ReminderRepositoryContainer


class ServiceFactory:
    @staticmethod
    def create_reminder_config_service() -> ReminderConfigService:
        return ReminderConfigService(ReminderRepositoryContainer.reminder_config_repository())

    @staticmethod
    def create_reminder_message_service() -> ReminderMessageService:
        return ReminderMessageService(ReminderRepositoryContainer.reminder_message_repository())
