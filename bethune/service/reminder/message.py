from bethune.model.reminder import ReminderMessage
from bethune.repository.reminder.message import ReminderMessageRepository
from bethune.service.base import BaseService


class ReminderMessageService(BaseService[ReminderMessage, ReminderMessageRepository]):

    def __init__(self, repository: ReminderMessageRepository):
        super().__init__(repository)

    def get_messages(self, receiver_id: int, read_status: int | None = None) -> list[ReminderMessage]:
        return self.repository.get_by_receiver(receiver_id, read_status)

    def get_unread_count(self, receiver_id: int) -> int:
        return self.repository.get_unread_count(receiver_id)

    def mark_message_as_read(self, message_id: int, broker_id: int) -> ReminderMessage:
        return self.repository.mark_as_read(message_id, broker_id)

    def create_message(self, message: ReminderMessage) -> ReminderMessage:
        return self.repository.create(message)

    async def mark_messages_as_read(self, broker_id: int):
        return await self.repository.mark_all_as_read(broker_id)

    def paged_list(
        self, example: ReminderMessage | None = None, offset: int = 0, limit: int = 20
    ) -> tuple[int, list[ReminderMessage]]:
        return self.repository.paged_list(example, offset, limit)

    def get_user_language_by_user_id(self, user_id: int) -> str:
        return self.repository.get_user_language_by_user_id(user_id)
