from enum import StrEnum

from pydantic import Field
from sqlalchemy.dialects.mysql import SET
from sqlmodel import Column
from sqlmodel import UniqueConstraint

from bethune.model.base import AuditMixin
from bethune.model.base import BaseModel
from bethune.model.base import ExtendedAuditMixin


class BusinessType(StrEnum):
    """业务类型"""

    POLICY_RENEWAL = "POLICY_RENEWAL"  # 续保
    PREMIUM_CALCULATED = "PREMIUM_CALCULATED"  # 保费计算
    POLICY_REJECT = "POLICY_REJECT"  # 退回
    POLICY_ISSUED = "POLICY_ISSUED"  # 保单承保
    OTHER = "OTHER"  # 其它


class ReminderType(StrEnum):
    """提醒类型"""

    BROKER = "BROKER"
    CUSTOMER = "CUSTOMER"


class NotifyMethod(StrEnum):
    """通知方式"""

    INBOX = "INBOX"
    EMAIL = "EMAIL"
    SMS = "SMS"


class ReminderConfig(BaseModel, AuditMixin, table=True):  # type: ignore
    __tablename__ = "reminder_config"
    __table_args__ = (
        UniqueConstraint("broker_id", "reminder_type", "business_type", name="uq_broker_reminder_business"),
    )

    broker_id: int = Field(foreign_key="broker.id", index=True, description="代理人ID")
    reminder_type: ReminderType
    first_reminder_days: int = 0
    second_reminder_days: int = 0
    business_type: BusinessType
    notify_methods: str = Field(..., sa_column=Column(SET(*[NotifyMethod]), nullable=False, comment="通知方式"))
    enabled: bool = True

    class Config:
        arbitrary_types_allowed = True

    @property
    def notify_methods_list(self) -> list[str]:
        return self.notify_methods.split(",") if self.notify_methods else []

    @notify_methods_list.setter
    def notify_methods_list(self, value: list[str]):
        if value is not None and isinstance(value, list):
            self.notify_methods = ",".join(value)
        else:
            self.notify_methods = f"{NotifyMethod.EMAIL.value}"  # set default value


class ReminderMessage(BaseModel, ExtendedAuditMixin, table=True):  # type: ignore
    __tablename__ = "reminder_message"

    business_type: BusinessType
    read_status: int = 0
    content: str  # 存储msgid
    extra_content: str | None = None
    sender_id: int = 0
    receiver_id: int
    context: str | None = None  # 存储JSON字符串格式的上下文信息
