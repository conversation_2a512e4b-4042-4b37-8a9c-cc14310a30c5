# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Python-based backend service for a Canadian home insurance platform built with FastAPI. The project follows a layered architecture with clear separation of concerns:

- **API Layer**: HTTP endpoints and request/response DTOs
- **Service Layer**: Business logic implementation
- **Repository Layer**: Data access and persistence
- **Model Layer**: Database models and ORM definitions

## Common Development Commands

### Environment Setup
```bash
# Create and activate conda environment
conda env create
conda activate comintern_bethune_ve

# Install Python dependencies
poetry install --all-groups

# Install Node.js dependencies for git hooks
yarn install
```

### Development Workflow
```bash
# Run pre-commit checks (executed automatically on git commit)
conda activate comintern_bethune_ve
pre-commit run -a

# Run tests
pytest

# Run tests with coverage
pytest --cov=bethune --cov-report=html

# Run specific test file
pytest tests/path/to/test_file.py

# Run application
python -m uvicorn bethune.app:app --reload
```

### Code Quality
```bash
# Run linting
flake8 .

# Run type checking
mypy .

# Format code
black .
```

## Codebase Architecture

### Project Structure
- `bethune/api/` - REST API endpoints, DTOs, and error handling
- `bethune/service/` - Business logic services
- `bethune/repository/` - Data access layer
- `bethune/model/` - Database models
- `bethune/db/` - Database configuration and connection management
- `bethune/settings/` - Application configuration
- `tests/` - Unit and integration tests

### Key Patterns
1. **Dependency Injection**: Database sessions are injected via FastAPI dependencies
2. **DTO Pattern**: Separate data transfer objects for API requests/responses
3. **Repository Pattern**: Data access is abstracted through repository classes
4. **Service Layer**: Business logic is encapsulated in service classes
5. **Error Handling**: Custom exceptions with proper HTTP status codes

### Database
- MySQL as the primary database
- SQLAlchemy as the ORM
- Redis for caching
- Alembic-style migrations in `db/scripts/`

### Internationalization
- Uses `fastapi-babel` for i18n
- Translation files in `bethune/locales/`
- Supports English, French, and Chinese
