import pytest
import os
from bethune.util.i18n import get_text


def test_translation_basic_functionality():
    """测试翻译功能是否正常工作"""
    original_lang = os.environ.get('LANGUAGE')
    try:
        os.environ['LANGUAGE'] = 'zh'
        # 使用一个已知存在的简单翻译测试
        translation = get_text("VerificationCode_Signature")
        print(f"测试翻译结果: {translation}")
        # 如果翻译正常工作，应该返回中文而不是原msgid
        assert translation != "VerificationCode_Signature"
    finally:
        if original_lang:
            os.environ['LANGUAGE'] = original_lang
        else:
            os.environ.pop('LANGUAGE', None)


@pytest.mark.parametrize("msgid,expected_contains", [
    ("InboxMessage_PolicyRenewalReminder", "历史保单将于"),
    ("InboxMessage_DefaultInsuranceReminder", "新的保险提醒消息"),
    ("InboxMessage_PremiumCalculatedReminder", "保费计算已完成"),
    ("InboxMessage_PolicyRejectReminder", "保单申请被退回"),
    ("InboxMessage_PolicyIssuedReminder", "保单已成功承保"),
    ("InboxMessage_OtherReminder", "新的业务提醒"),
])
def test_inbox_message_translations(msgid, expected_contains):
    original_lang = os.environ.get('LANGUAGE')
    try:
        os.environ['LANGUAGE'] = 'zh'
        translation = get_text(msgid)
        print(f"msgid: {msgid}")
        print(f"translation: {translation}")

        # 如果翻译功能正常，translation应该不等于msgid
        if translation == msgid:
            pytest.skip(f"翻译功能未正常工作，{msgid} 未找到翻译")

        assert expected_contains in translation
    finally:
        if original_lang:
            os.environ['LANGUAGE'] = original_lang
        else:
            os.environ.pop('LANGUAGE', None)


def test_inbox_message_msgid_format():
    inbox_msgids = [
        "InboxMessage_PolicyRenewalReminder",
        "InboxMessage_DefaultInsuranceReminder", 
        "InboxMessage_PremiumCalculatedReminder",
        "InboxMessage_PolicyRejectReminder",
        "InboxMessage_PolicyIssuedReminder",
        "InboxMessage_OtherReminder"
    ]
    
    for msgid in inbox_msgids:
        assert msgid.startswith("InboxMessage_")
        assert '_' in msgid
        parts = msgid.split('_')
        assert len(parts) == 2
        assert parts[0] == "InboxMessage"
        assert parts[1][0].isupper()
