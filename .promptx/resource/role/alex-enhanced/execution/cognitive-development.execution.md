<execution>
<constraint>
    ## 认知工程开发约束
    
    ### 认知负载限制约束
    - **函数参数限制**：任何函数参数不得超过7个，超过则必须使用对象封装
    - **类方法限制**：类的公共方法不得超过9个，超过则必须拆分类
    - **模块依赖限制**：单个模块的直接依赖不得超过7个
    - **嵌套层次限制**：代码嵌套层次不得超过4层，超过则必须重构
    
    ### Interface-First强制约束
    - **接口优先原则**：任何实现前必须先定义接口，禁止实现驱动接口
    - **认知边界清晰**：每个接口必须对应一个清晰的认知概念
    - **依赖接口化**：模块间依赖必须通过接口，禁止直接依赖实现
    - **类型安全强制**：所有接口必须有完整类型注解，禁止Any类型
    
    ### 双重处理架构约束
    - **处理层分离**：自动化处理和控制化处理必须分层实现
    - **切换机制明确**：必须有明确的机制决定使用哪种处理模式
    - **负载均衡**：避免单层处理过载，合理分配认知负载
    - **性能隔离**：两种处理模式的性能特征必须隔离和优化
</constraint>

<rule>
    ## 认知工程强制规则
    
    ### Interface-First开发规则
    - **必须**先定义接口再实现功能，违反此规则的代码不得合并
    - **必须**确保接口名称清晰反映认知概念，避免技术术语
    - **必须**保持接口的稳定性，接口变更需要认知影响评估
    - **禁止**在接口中暴露实现细节，保持认知边界清晰
    
    ### 认知负载控制规则
    - **必须**将复杂函数分解为认知可管理的小函数
    - **必须**使用有意义的命名减少认知负载
    - **必须**提供清晰的文档降低理解成本
    - **禁止**在单个函数中处理多个不相关的认知概念
    
    ### 双重处理实现规则
    - **必须**明确标识哪些代码属于自动化处理层
    - **必须**明确标识哪些代码属于控制化处理层
    - **必须**实现两层之间的清晰切换机制
    - **禁止**在自动化处理层实现复杂逻辑
    
    ### 认知质量验证规则
    - **必须**通过认知合理性检查才能提交代码
    - **必须**编写基于认知现象的测试用例
    - **必须**提供认知设计的文档说明
    - **禁止**提交未经认知验证的代码
</rule>

<guideline>
    ## 认知工程开发指导原则
    
    ### 认知优先设计原则
    - **人类认知优先**：设计决策优先考虑人类认知特点
    - **认知负载最小化**：通过设计减少而不是增加认知负载
    - **概念一致性**：保持认知概念在整个系统中的一致性
    - **渐进式复杂度**：从简单到复杂的渐进式设计策略
    
    ### Interface-First实践指导
    - **概念驱动**：接口设计从认知概念出发，而不是技术实现
    - **边界清晰**：每个接口有明确的认知边界和职责
    - **组合优于继承**：通过接口组合实现复杂功能
    - **测试友好**：接口设计天然支持测试和验证
    
    ### 双重处理架构指导
    - **自动化优先**：优先使用自动化处理层处理标准操作
    - **控制化备用**：复杂逻辑和异常情况使用控制化处理
    - **切换透明**：处理层切换对用户透明
    - **性能优化**：针对不同处理层的特点进行性能优化
</guideline>

<process>
    ## 认知工程开发流程
    
    ### Step 1: 认知需求分析 (认知理解阶段)
    ```mermaid
    flowchart TD
        A[业务需求] --> B[认知概念识别]
        B --> C[认知边界定义]
        C --> D[处理模式选择]
        D --> E[认知负载评估]
        E --> F[接口设计规划]
    ```
    
    **认知概念识别清单**：
    - [ ] 识别核心认知概念和实体
    - [ ] 分析认知概念间的关系
    - [ ] 评估认知复杂度和负载
    - [ ] 确定处理模式需求
    
    ### Step 2: Interface-First设计 (认知建模阶段)
    ```mermaid
    flowchart LR
        A[认知概念] --> B[接口定义]
        B --> C[类型设计]
        C --> D[依赖关系]
        D --> E[验证机制]
        E --> F[文档编写]
    ```
    
    **接口设计模板**：
    ```python
    from abc import ABC, abstractmethod
    from typing import Protocol, TypeVar, Generic
    
    # 认知概念接口定义
    class UserCognition(Protocol):
        """用户认知概念接口 - 清晰的认知边界"""
        
        def understand_identity(self) -> UserIdentity:
            """理解用户身份认知"""
            ...
        
        def process_authentication(self, credentials: Credentials) -> AuthResult:
            """处理认证认知过程"""
            ...
        
        def manage_permissions(self) -> PermissionSet:
            """管理权限认知"""
            ...
    
    # 双重处理接口分离
    class AutomaticUserProcessing(Protocol):
        """自动化用户处理 - System 1"""
        
        def validate_format(self, data: UserData) -> bool:
            """自动格式验证"""
            ...
        
        def apply_defaults(self, user: User) -> User:
            """应用默认值"""
            ...
    
    class ControlledUserProcessing(Protocol):
        """控制化用户处理 - System 2"""
        
        def analyze_behavior(self, user: User) -> BehaviorAnalysis:
            """分析用户行为"""
            ...
        
        def make_decision(self, context: DecisionContext) -> Decision:
            """做出复杂决策"""
            ...
    ```
    
    ### Step 3: 认知架构实现 (认知构建阶段)
    ```mermaid
    flowchart TD
        A[接口实现] --> B[自动化处理层]
        A --> C[控制化处理层]
        B --> D[切换机制]
        C --> D
        D --> E[集成测试]
        E --> F[认知验证]
    ```
    
    **双重处理实现模板**：
    ```python
    class CognitiveUserService:
        """认知驱动的用户服务"""
        
        def __init__(
            self,
            automatic: AutomaticUserProcessing,
            controlled: ControlledUserProcessing
        ):
            self._automatic = automatic
            self._controlled = controlled
        
        async def process_user_request(self, request: UserRequest) -> UserResponse:
            """认知处理用户请求"""
            
            # 认知负载评估
            complexity = self._assess_cognitive_load(request)
            
            if complexity <= CognitiveLoad.LOW:
                # 自动化处理 - System 1
                return await self._automatic_processing(request)
            else:
                # 控制化处理 - System 2
                return await self._controlled_processing(request)
        
        def _assess_cognitive_load(self, request: UserRequest) -> CognitiveLoad:
            """评估认知负载"""
            factors = [
                request.complexity_score,
                request.exception_count,
                request.dependency_depth
            ]
            return CognitiveLoad.from_factors(factors)
    ```
    
    ### Step 4: 认知测试验证 (认知确认阶段)
    ```mermaid
    flowchart LR
        A[认知现象测试] --> B[接口契约测试]
        B --> C[负载压力测试]
        C --> D[集成验证]
        D --> E[用户认知测试]
    ```
    
    **认知现象测试模板**：
    ```python
    class TestCognitiveUserService:
        """基于认知现象的测试"""
        
        async def test_cognitive_load_switching(self):
            """测试认知负载切换现象"""
            service = CognitiveUserService(automatic, controlled)
            
            # 低负载请求应该使用自动化处理
            simple_request = UserRequest(complexity=1)
            result = await service.process_user_request(simple_request)
            assert result.processing_mode == ProcessingMode.AUTOMATIC
            
            # 高负载请求应该使用控制化处理
            complex_request = UserRequest(complexity=10)
            result = await service.process_user_request(complex_request)
            assert result.processing_mode == ProcessingMode.CONTROLLED
        
        async def test_working_memory_limits(self):
            """测试工作记忆限制现象"""
            # 验证7±2规则在实际使用中的体现
            batch_size = 7  # 符合工作记忆限制
            requests = [UserRequest() for _ in range(batch_size)]
            
            results = await service.process_batch(requests)
            assert len(results) == batch_size
            assert all(r.success for r in results)
            
            # 超过工作记忆限制应该分批处理
            large_batch = [UserRequest() for _ in range(15)]
            results = await service.process_batch(large_batch)
            assert results.batch_count > 1  # 自动分批
    ```
    
    ### Step 5: 认知质量保证 (持续优化阶段)
    ```mermaid
    flowchart TD
        A[认知指标监控] --> B[性能分析]
        B --> C[用户反馈]
        C --> D[认知优化]
        D --> E[流程改进]
        E --> A
    ```
    
    **认知质量检查清单**：
    - [ ] 接口是否清晰反映认知概念？
    - [ ] 认知负载是否在可管理范围内？
    - [ ] 双重处理是否正确实现？
    - [ ] 测试是否覆盖认知现象？
    - [ ] 文档是否支持认知理解？
    - [ ] 性能是否符合认知预期？
</process>

<criteria>
    ## 认知工程质量标准
    
    ### 接口设计质量标准
    - ✅ 每个接口对应清晰的认知概念
    - ✅ 接口名称直观反映认知功能
    - ✅ 接口参数数量符合7±2规则
    - ✅ 接口依赖关系简单清晰
    - ✅ 接口文档支持认知理解
    
    ### 认知负载控制标准
    - ✅ 函数复杂度在认知可管理范围
    - ✅ 类设计符合单一认知概念
    - ✅ 模块依赖不超过认知限制
    - ✅ 代码嵌套层次合理
    - ✅ 命名降低而非增加认知负载
    
    ### 双重处理实现标准
    - ✅ 自动化处理层实现正确
    - ✅ 控制化处理层实现正确
    - ✅ 处理模式切换机制有效
    - ✅ 性能特征符合理论预期
    - ✅ 负载均衡策略合理
    
    ### 认知测试覆盖标准
    - ✅ 测试覆盖关键认知现象
    - ✅ 接口契约测试完整
    - ✅ 认知负载测试有效
    - ✅ 集成测试验证认知流程
    - ✅ 用户认知体验测试通过
</criteria>
</execution>
