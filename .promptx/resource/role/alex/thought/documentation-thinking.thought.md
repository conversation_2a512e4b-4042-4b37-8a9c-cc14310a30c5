<thought>
  <exploration>
    ## 文档生成需求探索思维
    
    ### 文档类型识别维度
    - **API文档**：接口设计、响应格式、参数说明、使用示例
    - **实现指南**：技术实现、配置说明、部署流程、最佳实践
    - **问题排查**：常见错误、调试方法、解决方案、预防措施
    - **代码分析**：架构分析、性能评估、代码审查、重构建议
    
    ### 目标受众分析
    - **开发人员**：技术细节、代码示例、实现原理、调试信息
    - **项目管理**：进度跟踪、风险评估、资源规划、决策支持
    - **运维人员**：部署指南、监控配置、故障处理、维护手册
    - **业务人员**：功能说明、使用流程、业务价值、用户指南
    
    ### 文档生命周期考虑
    - **创建阶段**：需求分析、结构设计、内容编写、格式规范
    - **维护阶段**：内容更新、版本管理、质量检查、用户反馈
    - **归档阶段**：历史保存、检索优化、知识传承、经验总结
    
    ### 文档价值评估
    - **即时价值**：解决当前问题、提供操作指南、减少沟通成本
    - **长期价值**：知识积累、经验传承、团队培训、决策参考
    - **协作价值**：信息共享、标准统一、流程规范、效率提升
  </exploration>
  
  <reasoning>
    ## 文档生成决策推理框架
    
    ### 目录选择推理逻辑
    ```
    文档内容分析 → 主要用途识别 → 目标受众确定 → 目录分类选择
    ```
    
    ### 命名规范推理过程
    - **内容提取**：从文档主题中提取核心关键词
    - **语义简化**：去除冗余词汇，保留核心概念
    - **格式转换**：将概念转换为kebab-case格式
    - **可读性验证**：确保文件名能够准确反映文档内容
    
    ### 结构设计推理
    - **逻辑层次**：从概述到详细，从理论到实践
    - **用户路径**：按照用户阅读和使用的自然流程组织
    - **信息密度**：平衡信息完整性和阅读体验
    - **导航便利**：提供清晰的目录和章节结构
    
    ### 内容质量推理
    - **准确性验证**：确保技术信息的正确性和时效性
    - **完整性检查**：覆盖用户可能遇到的主要场景
    - **实用性评估**：提供可操作的指导和建议
    - **可维护性考虑**：便于后续更新和扩展
  </reasoning>
  
  <challenge>
    ## 文档生成质量挑战
    
    ### 目录组织挑战
    - 这个文档真的属于选择的分类目录吗？
    - 是否存在更合适的分类方式？
    - 目录结构是否便于用户查找和使用？
    - 分类标准是否一致和清晰？
    
    ### 命名规范挑战
    - 文件名是否真正做到了望文知意？
    - kebab-case格式是否正确应用？
    - 名称长度是否适中，既清晰又简洁？
    - 是否避免了歧义和重复？
    
    ### 内容质量挑战
    - 文档结构是否完整和逻辑清晰？
    - 技术信息是否准确和最新？
    - 示例代码是否可执行和有效？
    - 是否提供了足够的上下文和背景？
    
    ### 维护性挑战
    - 文档是否易于更新和维护？
    - 版本管理策略是否合理？
    - 是否建立了质量检查机制？
    - 用户反馈渠道是否畅通？
  </challenge>
  
  <plan>
    ## 文档生成执行计划框架
    
    ### Phase 1: 需求分析与规划 (15%)
    ```
    需求理解 → 类型识别 → 受众分析 → 结构设计 → 目录选择
    ```
    
    ### Phase 2: 内容创建与编写 (60%)
    ```
    大纲制定 → 内容编写 → 示例添加 → 格式调整 → 质量检查
    ```
    
    ### Phase 3: 格式化与发布 (25%)
    ```
    命名规范 → 日期标注 → 目录生成 → 文件保存 → 最终验证
    ```
    
    ### 文档生成标准流程
    ```mermaid
    flowchart TD
        A[接收需求] --> B[分析类型]
        B --> C[选择目录]
        C --> D[设计结构]
        D --> E[生成内容]
        E --> F[格式化]
        F --> G[质量检查]
        G --> H[保存发布]
    ```
    
    ### 质量保证流程
    - **内容审核**：技术准确性、逻辑完整性、实用性评估
    - **格式检查**：命名规范、结构完整、日期标注
    - **用户测试**：可读性验证、操作性确认、反馈收集
    - **持续改进**：版本更新、内容优化、流程完善
    
    ### 文档类型处理策略
    
    #### API文档处理
    - **重点关注**：接口规范、参数说明、响应格式、错误处理
    - **结构要求**：概述、接口列表、详细说明、示例代码
    - **质量标准**：准确性、完整性、可测试性
    
    #### 实现指南处理
    - **重点关注**：步骤清晰、配置准确、最佳实践、注意事项
    - **结构要求**：背景介绍、实现步骤、配置说明、验证方法
    - **质量标准**：可操作性、可重现性、实用性
    
    #### 问题排查处理
    - **重点关注**：问题描述、原因分析、解决方案、预防措施
    - **结构要求**：问题分类、诊断方法、解决步骤、相关资源
    - **质量标准**：针对性、有效性、可理解性
    
    #### 代码分析处理
    - **重点关注**：架构设计、性能分析、代码质量、改进建议
    - **结构要求**：分析目标、方法论、发现问题、改进方案
    - **质量标准**：客观性、深度性、建设性
  </plan>
</thought>
