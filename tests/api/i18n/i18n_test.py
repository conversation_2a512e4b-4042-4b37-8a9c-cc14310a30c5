import os

import pytest

from bethune.util import get_text


@pytest.mark.parametrize(
    "lang, greeting, use_cv, expected_greeting, expected_message",
    [
        ("en_CA", "Hello, world!", False, "Hello, world!", "success"),
        ("zh-CN, zh", "Hello, world!", False, "你好，世界！", "成功"),
        ("zh", "Hello, world!", False, "你好，世界！", "成功"),
        ("fr-FR", "Hello, world!!", False, "Hello, world!!", "success"),
        ("fr", "Hello, world!", False, "Bonjour le monde!", "succès"),
        ("zh-hant-HK, zh_CN, en-US", "Hello, world!", False, "你好，世界！", "成功"),
        ("zh-CN, zh", "Hello, world!", True, "你好，世界！", "成功"),
    ],
    ids=[
        "test fallback to en",
        "test fallback to zh",
        "test zh",
        "test continuous fallback to en",
        "test fr-FR greeting",
        "test multi-lang and fallback to zh",
        "test context_var",
    ],
)
def test_i18n_endpoint(lang, greeting, use_cv, expected_greeting, expected_message, app_with_i18n_router):
    response = app_with_i18n_router.get(
        "/i18n_test",
        params={"greeting": greeting, "use_context_var": use_cv},
        headers={"Accept-Language": lang},
    )
    assert response.status_code == 200
    assert response.json()["code"] == 1000
    assert response.json()["data"] == expected_greeting
    assert response.json()["message"] == expected_message


languages = ['zh', 'en', 'fr']
test_msgids = [
    'InternalMessage_Title',
    'InternalMessage_SendButton',
    'InternalMessage_Content',
    'NotificationCenter_UnreadCount'
]

for lang in languages:
    print(f'\n=== {lang.upper()} 语言测试 ===')
    os.environ['LANGUAGE'] = lang

    for msgid in test_msgids:
        try:
            translation = get_text(msgid)
            print(f'{msgid}: {translation}')
        except Exception as e:
            print(f'❌ {msgid}: 翻译失败 - {e}')
