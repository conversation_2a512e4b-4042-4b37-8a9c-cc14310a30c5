# test_inbox_message.py
import asyncio
import json
from bethune.infrastructure.task.implementations import InboxTask
from bethune.api.dto.base import BusinessType, InsuranceType
from bethune.infrastructure.task.messages import ReminderType


async def test_send_inbox_message():
    """手动测试发送站内信"""

    # 准备测试数据
    task_data = {
        "task_id": "test_001",
        "business_type": BusinessType.POLICY_RENEWAL.value,
        "insurance_type": InsuranceType.HOUSE_INSURANCE.value,
        "reminder_type": ReminderType.BROKER.value,
        "content": "InboxMessage_PolicyRenewalReminder",  # msgid
        "extra_content": json.dumps({"customer_name": "张三"}),
        "context": json.dumps({
            "customer_name": "张三",
            "expiration_date": "2024-12-31",
            "broker_name": "李经理",
            "receiver_id": 1
        }),
        "receiver_id": 1,  # 接收人ID（经纪人ID）
        "sender_id": 0  # 系统消息
    }

    # 创建并执行任务
    inbox_task = InboxTask(task_data)
    await inbox_task.run()

    print("站内信发送完成！")


if __name__ == "__main__":
    asyncio.run(test_send_inbox_message())
